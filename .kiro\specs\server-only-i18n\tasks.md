
# Implementation Plan

- [ ] 1. Implement core server-only translation utility
  - Create `src/lib/i18n/server.ts` with <PERSON>act's cache function
  - Implement `getLocale` function to read from headers
  - Add `getTranslations` function for namespace loading
  - Create `createTranslator` function with interpolation support
  - Implement fallback mechanism for missing translations
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [ ] 2. Enhance middleware for locale detection and header setting
  - Update `src/middleware.ts` to detect locale from cookies and headers
  - Add logic to set x-locale header for server components
  - Implement 302 redirects for SEO-friendly URLs
  - Add cookie-based locale persistence
  - Store original path in x-original-path header
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5_

- [ ] 3. Create SEO metadata generation utility
  - Implement `generateSEOMetadata` function in server utility
  - Add support for title, description, and Open Graph properties
  - Create hreflang link generation for all supported languages
  - Add structured data support with localization
  - Implement canonical URL generation
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 5.5_

- [ ] 4. Implement SEO-optimized LocalizedHead component
  - Create `src/components/seo/localized-head.tsx` component
  - Add support for all SEO metadata properties
  - Implement hreflang link rendering
  - Add structured data script injection
  - Support Open Graph and Twitter Card tags
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5_

- [ ] 5. Create translation types and interfaces
  - Define `SupportedLocale` type in `src/types/i18n.ts`
  - Create `TranslationProps` interface for Client Components
  - Add `SEOMetadata` interface for metadata generation
  - Implement `TranslationsMap` interface for namespace organization
  - Create utility types for translation functions
  - _Requirements: 1.5, 6.5, 7.1, 7.4_

- [ ] 6. Implement client component translation pattern
  - Create example Client Component with translation props
  - Implement `getAllTranslations` utility for multiple namespaces
  - Document pattern for passing translations from Server to Client Components
  - Add type safety for translation props
  - Create minimal client-side utilities if needed
  - _Requirements: 7.1, 7.2, 7.3, 7.4, 7.5_

- [ ] 7. Create validation and sitemap generation scripts
  - Implement `scripts/validate-translations.js` para checar se todas as chaves existem em todos os idiomas (build deve falhar se incompleto, integrado ao CI/CD)
  - Criar `scripts/generate-sitemap.js` para sitemap multilíngue
  - Adicionar npm scripts para builds de SEO e validação
  - Implementar detecção de traduções ausentes e logs de alerta
  - Gerar tipos TypeScript para as chaves de tradução
  - _Requirements: 6.1, 6.2, 6.3, 6.4, 6.5_

- [ ] 8. Migrate create-recipe page to server-only pattern
  - Atualizar página para usar apenas traduções server-only
  - Implementar geração de metadados SEO
  - Passar traduções para Client Components via props
  - Testar eliminação de FOUC e melhorias de performance
  - Verificar otimização de SEO com simulação de crawler
  - Dividir em subtarefas se necessário para facilitar revisão incremental
  - _Requirements: 2.1, 2.2, 2.3, 3.1, 3.2_

- [ ] 9. Create comprehensive testing suite
  - Write unit tests for server translation utility
  - Implement integration tests for SEO components
  - Add end-to-end tests for locale detection and redirects
  - Create performance tests for Core Web Vitals
  - Implement SEO validation tests
  - _Requirements: 2.4, 3.5, 4.1, 8.1, 8.2, 8.3_

- [ ] 10. Update Next.js configuration for optimal i18n and SEO
  - Enhance `next.config.ts` with proper i18n configuration
  - Add sitemap and robots.txt generation
  - Configure proper caching headers for translation assets
  - Implement performance optimizations
  - Add monitoring for Core Web Vitals
  - _Requirements: 3.6, 6.4, 8.4, 8.5_

- [ ] 11. Create migration documentation and guidelines
  - Documentar padrão server-only de tradução
  - Criar guia de migração step-by-step para páginas existentes
  - Adicionar melhores práticas para Server e Client Components
  - Documentar técnicas de otimização de SEO
  - Criar troubleshooting guide
  - Garantir que toda documentação siga o padrão step-by-step do projeto
  - _Requirements: 1.5, 7.2, 7.5_

- [ ] 12. Migrate remaining critical pages
  - Atualizar homepage para server-only translations
  - Migrar páginas do dashboard para o novo padrão
  - Converter componentes de autenticação para usar props de tradução
  - Testar todas as páginas para SEO e performance
  - Verificar eliminação de erros de roteamento
  - Dividir em subtarefas por página/componente para facilitar revisão
  - _Requirements: 2.1, 2.5, 3.1, 7.4, 8.3_