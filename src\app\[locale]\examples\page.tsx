/**
 * Example page showing how to use Client Components with server-only translations
 * Demonstrates the proper pattern for passing translations to Client Components
 */

import { createTranslator, getAllTranslations, getLocale } from '@/lib/i18n/server';
import { generateLocalizedMetadata } from '@/components/seo/localized-head';
import { 
  ClientButtonWithTranslations, 
  LocaleSwitcher, 
  InteractiveForm 
} from '@/components/examples/client-component-with-translations';
import { Metadata } from 'next';

interface ExamplesPageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: ExamplesPageProps): Promise<Metadata> {
  return await generateLocalizedMetadata({
    namespace: 'examples',
    pageKey: 'page',
  });
}

export default async function ExamplesPage({ params }: ExamplesPageProps) {
  const { locale } = await params;
  
  // Load server-side translations
  const t = await createTranslator('examples');
  const currentLocale = await getLocale();
  
  // Get translations for Client Components
  const { translations } = await getAllTranslations(['common', 'examples']);

  // Prepare translations for Client Components
  const buttonTranslations = {
    buttonText: t('button.text'),
    loading: t('button.loading'),
  };

  const localeSwitcherTranslations = {
    selectLanguage: t('locale-switcher.select-language'),
    english: t('locale-switcher.english'),
    portuguese: t('locale-switcher.portuguese'),
    spanish: t('locale-switcher.spanish'),
  };

  const formTranslations = {
    nameLabel: t('form.name-label'),
    namePlaceholder: t('form.name-placeholder'),
    nameRequired: t('form.name-required'),
    emailLabel: t('form.email-label'),
    emailPlaceholder: t('form.email-placeholder'),
    emailRequired: t('form.email-required'),
    emailInvalid: t('form.email-invalid'),
    messageLabel: t('form.message-label'),
    messagePlaceholder: t('form.message-placeholder'),
    messageRequired: t('form.message-required'),
    submitButton: t('form.submit-button'),
    submitting: t('form.submitting'),
    submitError: t('form.submit-error'),
  };

  // Server Action for form submission
  async function handleFormSubmit(data: Record<string, string>) {
    'use server';

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    // In a real app, you would save the data to a database
    console.log('Form submitted:', data);
    return { success: true };
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Server-rendered content */}
        <h1 className="text-4xl font-bold mb-2">
          {t('title')}
        </h1>
        <p className="text-lg text-gray-600 mb-8">
          {t('description')}
        </p>

        {/* Examples grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          
          {/* Client Button Example */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-2xl font-semibold mb-4">
              {t('examples.client-button.title')}
            </h2>
            <p className="text-gray-600 mb-4">
              {t('examples.client-button.description')}
            </p>
            
            <div className="space-y-4">
              <ClientButtonWithTranslations
                translations={buttonTranslations}
                locale={currentLocale}
                variant="primary"
              />

              <ClientButtonWithTranslations
                translations={buttonTranslations}
                locale={currentLocale}
                variant="secondary"
                disabled
              />
            </div>

            <div className="mt-4 p-4 bg-gray-50 rounded-md">
              <h3 className="font-medium text-sm text-gray-700 mb-2">
                {t('examples.client-button.code-title')}
              </h3>
              <pre className="text-xs text-gray-600 overflow-x-auto">
{`// Server Component
const buttonTranslations = {
  buttonText: t('button.text'),
  loading: t('button.loading'),
};

<ClientButtonWithTranslations
  translations={buttonTranslations}
  locale={currentLocale}
  onClick={handleClick}
/>`}
              </pre>
            </div>
          </div>

          {/* Locale Switcher Example */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-2xl font-semibold mb-4">
              {t('examples.locale-switcher.title')}
            </h2>
            <p className="text-gray-600 mb-4">
              {t('examples.locale-switcher.description')}
            </p>
            
            <LocaleSwitcher
              translations={localeSwitcherTranslations}
              locale={currentLocale}
              currentPath="/examples"
            />

            <div className="mt-4 p-4 bg-gray-50 rounded-md">
              <h3 className="font-medium text-sm text-gray-700 mb-2">
                {t('examples.locale-switcher.code-title')}
              </h3>
              <pre className="text-xs text-gray-600 overflow-x-auto">
{`// No router dependencies
const handleLocaleChange = (newLocale: string) => {
  const newPath = \`/\${newLocale}\${currentPath}\`;
  window.location.href = newPath;
};`}
              </pre>
            </div>
          </div>

          {/* Interactive Form Example */}
          <div className="bg-white rounded-lg shadow-md p-6 lg:col-span-2">
            <h2 className="text-2xl font-semibold mb-4">
              {t('examples.interactive-form.title')}
            </h2>
            <p className="text-gray-600 mb-6">
              {t('examples.interactive-form.description')}
            </p>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <InteractiveForm
                  translations={formTranslations}
                  locale={currentLocale}
                  onSubmit={handleFormSubmit}
                />
              </div>
              
              <div className="p-4 bg-gray-50 rounded-md">
                <h3 className="font-medium text-sm text-gray-700 mb-2">
                  {t('examples.interactive-form.code-title')}
                </h3>
                <pre className="text-xs text-gray-600 overflow-x-auto">
{`// Server Component prepares translations
const formTranslations = {
  nameLabel: t('form.name-label'),
  emailLabel: t('form.email-label'),
  submitButton: t('form.submit-button'),
  // ... other translations
};

// Server Action
async function handleFormSubmit(data) {
  'use server';
  // Process form data
}

<InteractiveForm
  translations={formTranslations}
  locale={currentLocale}
  onSubmit={handleFormSubmit}
/>`}
                </pre>
              </div>
            </div>
          </div>
        </div>

        {/* Benefits section */}
        <div className="mt-12 bg-blue-50 rounded-lg p-6">
          <h2 className="text-2xl font-semibold mb-4">
            {t('benefits.title')}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-white font-bold">✓</span>
              </div>
              <h3 className="font-medium mb-1">{t('benefits.no-fouc.title')}</h3>
              <p className="text-sm text-gray-600">{t('benefits.no-fouc.description')}</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-white font-bold">⚡</span>
              </div>
              <h3 className="font-medium mb-1">{t('benefits.performance.title')}</h3>
              <p className="text-sm text-gray-600">{t('benefits.performance.description')}</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-white font-bold">🔍</span>
              </div>
              <h3 className="font-medium mb-1">{t('benefits.seo.title')}</h3>
              <p className="text-sm text-gray-600">{t('benefits.seo.description')}</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-white font-bold">🛡️</span>
              </div>
              <h3 className="font-medium mb-1">{t('benefits.hydration.title')}</h3>
              <p className="text-sm text-gray-600">{t('benefits.hydration.description')}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export async function generateStaticParams() {
  return [
    { locale: 'en' },
    { locale: 'pt' },
    { locale: 'es' },
  ];
}
