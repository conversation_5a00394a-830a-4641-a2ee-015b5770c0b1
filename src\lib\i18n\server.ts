/**
 * Server-only i18n utilities for Next.js App Router
 * Pure server-side translation system with React cache() for optimal performance
 * Eliminates client-side dependencies and FOUC
 */

import { cache } from 'react';
import { headers } from 'next/headers';
import { getCurrentUserProfile } from '@/features/user-auth-data/services/profile.service';
import { getServerAuthState } from '@/features/auth/services/auth-state.service';
import type { SupportedLocale, SEOMetadata, HreflangLink } from '@/types/i18n';

/**
 * Get the current locale from headers (set by middleware)
 * Uses React cache() for optimal performance
 */
export const getLocale = cache(async (): Promise<SupportedLocale> => {
  const headersList = await headers();
  const locale = headersList.get('x-locale') || 'en';
  return ['en', 'pt', 'es'].includes(locale) ? (locale as SupportedLocale) : 'en';
});

/**
 * Get locale directly from URL params (alternative approach for App Router)
 * Use this when you have access to params in your page/layout components
 */
export function getLocaleFromParams(locale: string): SupportedLocale {
  return ['en', 'pt', 'es'].includes(locale) ? (locale as SupportedLocale) : 'en';
}

/**
 * Enhanced locale detection with user preference integration
 * Priority: 1. URL locale (SEO), 2. User DB preference, 3. Headers/Browser, 4. Default
 * Uses React cache() for optimal performance
 */
export const getLocaleWithUserPreference = cache(async (urlLocale?: string): Promise<SupportedLocale> => {
  // 1. URL locale takes highest priority (for SEO and direct access)
  if (urlLocale && ['en', 'pt', 'es'].includes(urlLocale)) {
    return urlLocale as SupportedLocale;
  }

  // 2. Try to get user's saved language preference from database
  try {
    const { user } = await getServerAuthState();
    if (user?.id) {
      const profile = await getCurrentUserProfile(user.id);
      if (profile?.language && ['en', 'pt', 'es'].includes(profile.language)) {
        return profile.language as SupportedLocale;
      }
    }
  } catch (error) {
    // Silently handle auth/profile errors - fallback to other methods
  }

  // 3. Fallback to header-based detection (middleware, browser, etc.)
  return await getLocale();
});

/**
 * Load translations for a specific namespace and locale
 * Uses React cache() for optimal performance and automatic deduplication
 */
export const getTranslations = cache(async (namespace: string, locale?: SupportedLocale) => {
  const targetLocale = locale || await getLocale();
  try {
    const translations = await import(`@/lib/i18n/messages/${targetLocale}/${namespace}.json`);
    return translations.default;
  } catch (error) {
    // Fallback to English if translation file is missing
    if (targetLocale !== 'en') {
      try {
        const fallbackTranslations = await import(`@/lib/i18n/messages/en/${namespace}.json`);
        // Log warning for missing translations
        console.warn(`[i18n] Missing translation file for ${targetLocale}/${namespace}, falling back to English`);
        return fallbackTranslations.default;
      } catch (fallbackError) {
        // Log critical error
        console.error(`[i18n] Missing translation file for en/${namespace}`);
        return {};
      }
    }
    console.error(`[i18n] Missing translation file for ${targetLocale}/${namespace}`);
    return {};
  }
});

/**
 * Create a translator function for a specific namespace and locale
 * Uses React cache() for optimal performance
 */
export const createTranslator = cache(async (namespace: string, locale?: SupportedLocale) => {
  const translations = await getTranslations(namespace, locale);
  const targetLocale = locale || await getLocale();

  return (key: string, params?: Record<string, any>): string => {
    // Handle nested keys with dot notation
    let translation = getNestedValue(translations, key);

    // Fallback to key if translation not found
    if (translation === undefined) {
      translation = key;

      // Log warning for missing keys in development
      if (process.env.NODE_ENV === 'development') {
        console.warn(`[i18n] Missing translation key: ${key} in namespace: ${namespace} for locale: ${targetLocale}`);
      }
    }

    // Simple interpolation
    if (params && typeof translation === 'string') {
      Object.entries(params).forEach(([param, value]) => {
        translation = translation.replace(`{{${param}}}`, String(value));
      });
    }

    return String(translation);
  };
});

/**
 * Enhanced translator with user preference integration
 * Automatically detects locale from URL params and user preferences
 */
export const createTranslatorWithUserPreference = cache(async (namespace: string, urlLocale?: string) => {
  const targetLocale = await getLocaleWithUserPreference(urlLocale);
  return createTranslator(namespace, targetLocale);
});

/**
 * Get optimal locale for a page with user preference integration
 * Use this in page components to get the best locale for the user
 */
export const getOptimalLocale = cache(async (urlLocale?: string): Promise<SupportedLocale> => {
  return getLocaleWithUserPreference(urlLocale);
});

/**
 * Enhanced getAllTranslations with user preference integration
 */
export const getAllTranslationsWithUserPreference = cache(async (namespaces: string[], urlLocale?: string) => {
  const targetLocale = await getLocaleWithUserPreference(urlLocale);
  return getAllTranslations(namespaces, targetLocale);
});

/**
 * Get all translations for multiple namespaces
 * Uses React cache() for optimal performance
 */
export const getAllTranslations = cache(async (namespaces: string[], locale?: SupportedLocale) => {
  const targetLocale = locale || await getLocale();
  const translations: Record<string, Record<string, string>> = {};

  await Promise.all(
    namespaces.map(async (namespace) => {
      translations[namespace] = await getTranslations(namespace, targetLocale);
    })
  );

  return { locale: targetLocale, translations };
});

/**
 * Generate SEO metadata with translations
 * Uses React cache() for optimal performance
 */
export const generateSEOMetadata = cache(async (namespace: string, pageKey: string) => {
  const locale = await getLocale();
  const translations = await getTranslations(namespace);
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://yoursite.com';

  const title = translations[`${pageKey}_meta_title`] || translations[`${pageKey}_title`] || pageKey;
  const description = translations[`${pageKey}_meta_description`] || '';

  // Generate hreflang links
  const headersList = await headers();
  const path = headersList.get('x-original-path') || '/';
  const hreflangLinks = ['en', 'pt', 'es'].map((lang) => ({
    hrefLang: lang,
    href: `${baseUrl}/${lang}${path}`
  }));

  return {
    title,
    description,
    locale,
    hreflangLinks,
    ogTitle: title,
    ogDescription: description,
    canonicalUrl: `${baseUrl}/${locale}${path}`
  };
});

/**
 * Helper function to get nested value using dot notation
 */
function getNestedValue(obj: any, path: string): string | undefined {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}