/**
 * Server-side i18n utilities for SSR/SSG with SEO optimization
 * Ensures translations are available at build/render time
 */

import { GetServerSidePropsContext, GetStaticPropsContext } from 'next';
import { loadNamespacedTranslations, type SupportedLocale, type NamespaceTranslations } from './index';

/**
 * Get locale from Next.js context (SSR/SSG)
 */
export function getLocaleFromContext(
  context: GetServerSidePropsContext | GetStaticPropsContext
): SupportedLocale {
  const locale = context.locale as SupportedLocale;
  return ['en', 'pt', 'es'].includes(locale) ? locale : 'en';
}

/**
 * Server-side translation function for SSR/SSG
 * Pre-loads all translations to avoid client-side loading
 */
export async function getServerTranslations(locale: SupportedLocale) {
  const translations = await loadNamespacedTranslations(locale);
  
  // Create server-side t function
  const t = (key: string, fallback?: string, variables?: Record<string, string | number>): string => {
    let text: string | undefined;

    if (key.includes(':')) {
      const [namespace, ...keyParts] = key.split(':');
      const keyPath = keyParts.join(':');
      
      if (namespace && translations[namespace]) {
        text = getNestedValue(translations[namespace], keyPath);
      }
    }

    text = text || fallback || key;

    // Variable interpolation
    if (variables && typeof text === 'string') {
      Object.entries(variables).forEach(([varKey, value]) => {
        text = text!.replace(new RegExp(`\\{${varKey}\\}`, 'g'), String(value));
      });
    }

    return text;
  };

  return { t, translations, locale };
}

/**
 * Get nested value helper
 */
function getNestedValue(obj: any, path: string): string | undefined {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

/**
 * Generate SEO-optimized metadata for different locales
 */
export function getLocalizedSEOMetadata(locale: SupportedLocale) {
  const seoData = {
    en: {
      title: 'Essential Oil Recipe Creator - Personalized Aromatherapy',
      description: 'Create personalized essential oil recipes based on your health concerns. Get expert aromatherapy recommendations tailored to your needs.',
      keywords: 'essential oils, aromatherapy, natural health, wellness, personalized recipes',
      ogTitle: 'Essential Oil Recipe Creator',
      ogDescription: 'Discover personalized essential oil recipes for your wellness journey',
    },
    pt: {
      title: 'Criador de Receitas de Óleos Essenciais - Aromaterapia Personalizada',
      description: 'Crie receitas personalizadas de óleos essenciais baseadas em suas preocupações de saúde. Receba recomendações especializadas de aromaterapia.',
      keywords: 'óleos essenciais, aromaterapia, saúde natural, bem-estar, receitas personalizadas',
      ogTitle: 'Criador de Receitas de Óleos Essenciais',
      ogDescription: 'Descubra receitas personalizadas de óleos essenciais para sua jornada de bem-estar',
    },
    es: {
      title: 'Creador de Recetas de Aceites Esenciales - Aromaterapia Personalizada',
      description: 'Crea recetas personalizadas de aceites esenciales basadas en tus preocupaciones de salud. Obtén recomendaciones especializadas de aromaterapia.',
      keywords: 'aceites esenciales, aromaterapia, salud natural, bienestar, recetas personalizadas',
      ogTitle: 'Creador de Recetas de Aceites Esenciales',
      ogDescription: 'Descubre recetas personalizadas de aceites esenciales para tu viaje de bienestar',
    },
  };

  return seoData[locale];
}

/**
 * Generate hreflang links for international SEO
 */
export function generateHreflangLinks(currentPath: string) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://yoursite.com';
  
  return [
    { hrefLang: 'en', href: `${baseUrl}/en${currentPath}` },
    { hrefLang: 'pt', href: `${baseUrl}/pt${currentPath}` },
    { hrefLang: 'es', href: `${baseUrl}/es${currentPath}` },
    { hrefLang: 'x-default', href: `${baseUrl}${currentPath}` },
  ];
}

/**
 * Server-side props helper for pages with i18n
 */
export async function getI18nServerSideProps(
  context: GetServerSidePropsContext,
  additionalProps?: any
) {
  const locale = getLocaleFromContext(context);
  const { t, translations } = await getServerTranslations(locale);
  const seoMetadata = getLocalizedSEOMetadata(locale);
  const hreflangLinks = generateHreflangLinks(context.resolvedUrl || '/');

  return {
    props: {
      locale,
      translations,
      seoMetadata,
      hreflangLinks,
      ...additionalProps,
    },
  };
}

/**
 * Static props helper for pages with i18n
 */
export async function getI18nStaticProps(
  context: GetStaticPropsContext,
  additionalProps?: any
) {
  const locale = getLocaleFromContext(context);
  const { t, translations } = await getServerTranslations(locale);
  const seoMetadata = getLocalizedSEOMetadata(locale);

  return {
    props: {
      locale,
      translations,
      seoMetadata,
      ...additionalProps,
    },
  };
}