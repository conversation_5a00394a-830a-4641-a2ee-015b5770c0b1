/**
 * Create Recipe page with server-only translations
 * Demonstrates the new server-side i18n pattern with no FOUC
 */

import { createTranslator, generateSEOMetadata, getAllTranslations } from '@/lib/i18n/server';
import { generateLocalizedMetadata } from '@/components/seo/localized-head';
import { Metadata } from 'next';

interface CreateRecipePageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: CreateRecipePageProps): Promise<Metadata> {
  const { locale } = await params;
  
  return await generateLocalizedMetadata({
    namespace: 'create-recipe',
    pageKey: 'page',
    additionalMetadata: {
      keywords: locale === 'pt' 
        ? 'receitas óleos essenciais, aromaterapia personalizada, saúde natural'
        : locale === 'es'
        ? 'recetas aceites esenciales, aromaterapia personalizada, salud natural'
        : 'essential oil recipes, personalized aromatherapy, natural health'
    }
  });
}

export default async function CreateRecipePage({ params }: CreateRecipePageProps) {
  const { locale } = await params;
  
  // Load translations for this page
  const t = await createTranslator('create-recipe');
  
  // Get translations for any client components that might need them
  const { translations } = await getAllTranslations(['common', 'create-recipe']);

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Server-rendered content - no FOUC! */}
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-center mb-2">
          {t('title')}
        </h1>
        <p className="text-lg text-gray-600 text-center mb-8">
          {t('subtitle')}
        </p>

        {/* Progress indicator */}
        <div className="mb-8">
          <div className="flex items-center justify-center space-x-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                1
              </div>
              <span className="ml-2 text-sm font-medium text-blue-600">
                {t('steps.demographics.title')}
              </span>
            </div>
            <div className="w-8 h-px bg-gray-300"></div>
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-medium">
                2
              </div>
              <span className="ml-2 text-sm text-gray-500">
                {t('steps.health-concerns.title')}
              </span>
            </div>
            <div className="w-8 h-px bg-gray-300"></div>
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-medium">
                3
              </div>
              <span className="ml-2 text-sm text-gray-500">
                {t('steps.symptoms.title')}
              </span>
            </div>
          </div>
        </div>

        {/* Demographics form */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-semibold mb-6">
            {t('steps.demographics.title')}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('steps.demographics.fields.age.label')}
              </label>
              <select className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <option value="">{t('steps.demographics.fields.age.placeholder')}</option>
                <option value="18-25">18-25</option>
                <option value="26-35">26-35</option>
                <option value="36-45">36-45</option>
                <option value="46-55">46-55</option>
                <option value="55+">{t('steps.demographics.fields.age.options.55plus')}</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('steps.demographics.fields.gender.label')}
              </label>
              <select className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <option value="">{t('steps.demographics.fields.gender.placeholder')}</option>
                <option value="male">{t('steps.demographics.fields.gender.options.male')}</option>
                <option value="female">{t('steps.demographics.fields.gender.options.female')}</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('steps.demographics.fields.language.label')}
              </label>
              <select className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <option value="">{t('steps.demographics.fields.language.placeholder')}</option>
                <option value="en">{t('steps.demographics.fields.language.options.english')}</option>
                <option value="pt">{t('steps.demographics.fields.language.options.portuguese')}</option>
                <option value="es">{t('steps.demographics.fields.language.options.spanish')}</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('steps.demographics.fields.experience.label')}
              </label>
              <select className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <option value="">{t('steps.demographics.fields.experience.placeholder')}</option>
                <option value="beginner">{t('steps.demographics.fields.experience.options.beginner')}</option>
                <option value="intermediate">{t('steps.demographics.fields.experience.options.intermediate')}</option>
                <option value="advanced">{t('steps.demographics.fields.experience.options.advanced')}</option>
              </select>
            </div>
          </div>

          <div className="mt-8 flex justify-end">
            <button className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-6 rounded-md transition-colors">
              {t('buttons.continue')}
            </button>
          </div>
        </div>

        {/* Benefits section */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-6">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold mb-2">
              {t('benefits.personalized.title')}
            </h3>
            <p className="text-gray-600">
              {t('benefits.personalized.description')}
            </p>
          </div>

          <div className="text-center p-6">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold mb-2">
              {t('benefits.expert.title')}
            </h3>
            <p className="text-gray-600">
              {t('benefits.expert.description')}
            </p>
          </div>

          <div className="text-center p-6">
            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold mb-2">
              {t('benefits.natural.title')}
            </h3>
            <p className="text-gray-600">
              {t('benefits.natural.description')}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export async function generateStaticParams() {
  return [
    { locale: 'en' },
    { locale: 'pt' },
    { locale: 'es' },
  ];
}
