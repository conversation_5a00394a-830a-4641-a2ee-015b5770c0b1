/**
 * Homepage with server-only translations
 * Demonstrates the new server-side i18n pattern
 */

import { createTranslator, generateSEOMetadata } from '@/lib/i18n/server';
import { Metadata } from 'next';

interface HomePageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: HomePageProps): Promise<Metadata> {
  const { locale } = await params;
  const seoData = await generateSEOMetadata('homepage', 'home');
  
  return {
    title: seoData.title,
    description: seoData.description,
    openGraph: {
      title: seoData.ogTitle,
      description: seoData.ogDescription,
      locale: seoData.locale,
    },
    alternates: {
      canonical: seoData.canonicalUrl,
      languages: Object.fromEntries(
        seoData.hreflangLinks.map(link => [link.hrefLang, link.href])
      ),
    },
  };
}

export default async function HomePage({ params }: HomePageProps) {
  const { locale } = await params;
  const t = await createTranslator('homepage');

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-4xl font-bold mb-6">
        {t('hero.title')}
      </h1>
      <p className="text-lg text-gray-600 mb-8">
        {t('hero.description')}
      </p>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="p-6 border rounded-lg">
          <h2 className="text-xl font-semibold mb-3">
            {t('features.personalized.title')}
          </h2>
          <p className="text-gray-600">
            {t('features.personalized.description')}
          </p>
        </div>
        <div className="p-6 border rounded-lg">
          <h2 className="text-xl font-semibold mb-3">
            {t('features.expert.title')}
          </h2>
          <p className="text-gray-600">
            {t('features.expert.description')}
          </p>
        </div>
        <div className="p-6 border rounded-lg">
          <h2 className="text-xl font-semibold mb-3">
            {t('features.natural.title')}
          </h2>
          <p className="text-gray-600">
            {t('features.natural.description')}
          </p>
        </div>
      </div>
    </div>
  );
}

export async function generateStaticParams() {
  return [
    { locale: 'en' },
    { locale: 'pt' },
    { locale: 'es' },
  ];
}
