/**
 * Simple test page for server-only i18n validation
 * Does not interfere with existing production features
 */

import { createTranslator } from '@/lib/i18n/server';
import { Metadata } from 'next';
import Link from 'next/link';

interface TestPageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: TestPageProps): Promise<Metadata> {
  const { locale } = await params;

  return {
    title: `Server-Only i18n Test - ${locale.toUpperCase()}`,
    description: 'Test page for validating server-only internationalization implementation',
  };
}

export default async function TestPage({ params }: TestPageProps) {
  const { locale } = await params;
  const t = await createTranslator('i18n-test');

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-4xl font-bold mb-6">
          {t('title')}
        </h1>
        <p className="text-lg text-gray-600 mb-8">
          {t('description')}
        </p>

        <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-green-800 mb-3">
            {t('status.title')}
          </h2>
          <p className="text-green-700">
            {t('status.message')}
          </p>
          <div className="mt-4 text-sm text-green-600">
            <p><strong>{t('current-locale')}:</strong> {locale}</p>
            <p><strong>{t('timestamp')}:</strong> {new Date().toISOString()}</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div className="p-6 border rounded-lg">
            <h3 className="text-lg font-semibold mb-3">
              {t('features.server-only.title')}
            </h3>
            <p className="text-gray-600">
              {t('features.server-only.description')}
            </p>
          </div>
          <div className="p-6 border rounded-lg">
            <h3 className="text-lg font-semibold mb-3">
              {t('features.no-fouc.title')}
            </h3>
            <p className="text-gray-600">
              {t('features.no-fouc.description')}
            </p>
          </div>
        </div>

        <div className="flex flex-wrap gap-4">
          <Link
            href="/en/i18n-test"
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            English
          </Link>
          <Link
            href="/pt/i18n-test"
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Português
          </Link>
          <Link
            href="/es/i18n-test"
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            Español
          </Link>
          <Link
            href="/en/examples"
            className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
          >
            {t('view-examples')}
          </Link>
        </div>
      </div>
    </div>
  );
}

export async function generateStaticParams() {
  return [
    { locale: 'en' },
    { locale: 'pt' },
    { locale: 'es' },
  ];
}
