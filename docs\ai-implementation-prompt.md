# 🤖 Prompt para IA: Implementar Internacionalização SEO-Otimizada

## 📋 CONTEXTO E PROBLEMA

Você está trabalhando em um projeto Next.js que tem um problema crítico de SEO com internacionalização:

**PROBLEMA ATUAL:**
- O site carrega primeiro as chaves de tradução (ex: "create-recipe:title") e depois o texto real
- Isso causa FOUC (Flash of Untranslated Content) - prejudicial para UX
- Crawlers do Google indexam as chaves técnicas em vez do conteúdo real
- Core Web Vitals ruins devido ao Cumulative Layout Shift (CLS)
- SEO internacional completamente comprometido

**OBJETIVO:**
Implementar uma solução completa de Server-Side Rendering (SSR) para i18n que elimine o FOUC e otimize o SEO internacional.

## 🎯 SOLUÇÃO A IMPLEMENTAR

Você deve implementar um sistema completo baseado nos arquivos de referência fornecidos, seguindo estas 5 estratégias principais:

### 1. **Server-Side Rendering (SSR) com Next.js i18n**
### 2. **Middleware de Detecção Inteligente de Idioma**  
### 3. **SEO Internacional com Hreflang**
### 4. **Componentes SEO-Otimizados**
### 5. **Scripts de Validação e Sitemap**

## 📁 ARQUIVOS DE REFERÊNCIA

Você tem acesso aos seguintes arquivos que contêm a implementação completa:

### **Arquivos Core do Sistema i18n:**
- `src/lib/i18n/server.ts` - Utilitários server-side para SSR/SSG
- `src/hooks/use-server-i18n.ts` - Hook client-side que usa traduções do servidor
- `src/components/seo/LocalizedHead.tsx` - Componente SEO otimizado (arrumar o nome para seguir o padrao do projeto)
- `src/middleware.ts` - Middleware para detecção de idioma e redirecionamentos

### **Arquivos de Configuração:**
- `next.config.ts` - Configuração Next.js com i18n
- `package.json` - Scripts de SEO e validação

### **Arquivos de Exemplo:**
- `pages/_app.tsx` - App component com suporte SSR i18n
- `pages/create-recipe.tsx` - Exemplo de página com SSR otimizado

### **Scripts Utilitários:**
- `scripts/generate-sitemap.js` - Geração de sitemap multilíngue
- `scripts/validate-translations.js` - Validação de traduções

### **Documentação:**
- `docs/i18n-seo-implementation-guide.md` - Guia completo de implementação

## 🚀 TAREFAS A EXECUTAR

### **FASE 1: Configuração Base**
1. **Analisar a estrutura atual** do projeto i18n em `src/lib/i18n/`
2. **Verificar arquivos de tradução** em `src/lib/i18n/messages/`
3. **Confirmar configuração** do Next.js já está correta

### **FASE 2: Implementação Core**
1. **Implementar `src/lib/i18n/server.ts`** - Sistema server-side completo
2. **Criar `src/hooks/use-server-i18n.ts`** - Hook otimizado para SSR
3. **Implementar `src/middleware.ts`** - Detecção automática de idioma
4. **Criar `src/components/seo/LocalizedHead.tsx`** - SEO internacional

### **FASE 3: Migração de Páginas**
1. **Atualizar `pages/_app.tsx`** - Suporte completo a SSR i18n
2. **Migrar páginas críticas** usando o padrão SSR:
   - `pages/create-recipe.tsx` (exemplo fornecido)
   - Outras páginas importantes do projeto
3. **Substituir `useI18n()` por `useServerI18n()`** nas páginas migradas

### **FASE 4: SEO e Scripts**
1. **Implementar scripts** em `scripts/`:
   - `generate-sitemap.js` - Sitemap multilíngue
   - `validate-translations.js` - Validação de traduções
2. **Configurar comandos npm** no `package.json`
3. **Testar geração de sitemap** e validação

### **FASE 5: Validação e Testes**
1. **Executar validação** de traduções
2. **Gerar sitemap** localizado
3. **Testar redirecionamentos** de idioma
4. **Verificar eliminação** do FOUC

## 📋 PADRÃO DE MIGRAÇÃO

### **ANTES (Problemático):**
```tsx
export default function MinhaPage() {
  const { t, isLoading } = useI18n(); // Client-side loading
  
  if (isLoading) return <div>Loading...</div>; // FOUC!
  
  return <h1>{t('create-recipe:title')}</h1>;
}
```

### **DEPOIS (SEO-Otimizado):**
```tsx
import { GetServerSideProps } from 'next';
import { useServerI18n } from '@/hooks/use-server-i18n';
import { LocalizedHead } from '@/components/seo/LocalizedHead';
import { getI18nServerSideProps } from '@/lib/i18n/server';

export default function MinhaPage({ locale, translations, seoMetadata, hreflangLinks }) {
  const { t } = useServerI18n({ locale, translations }); // Server-side!
  
  return (
    <>
      <LocalizedHead {...seoMetadata} hreflangLinks={hreflangLinks} />
      <h1>{t('create-recipe:title')}</h1> {/* Instantâneo! */}
    </>
  );
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  return getI18nServerSideProps(context);
};
```

## ✅ CRITÉRIOS DE SUCESSO

Após a implementação, o sistema deve:

### **Performance e UX:**
- ✅ **Zero FOUC** - conteúdo aparece instantaneamente
- ✅ **First Contentful Paint < 1s**
- ✅ **Cumulative Layout Shift < 0.1**
- ✅ **Conteúdo funciona sem JavaScript**

### **SEO Internacional:**
- ✅ **Hreflang links** corretos em todas as páginas
- ✅ **Meta tags localizadas** (title, description, og:*)
- ✅ **URLs localizadas** (/en/, /pt/, /es/)
- ✅ **Sitemap multilíngue** gerado automaticamente
- ✅ **Structured data** localizado

### **Funcionalidade:**
- ✅ **Detecção automática** de idioma por Accept-Language
- ✅ **Redirecionamentos corretos** para idioma detectado
- ✅ **Troca de idioma** funcional
- ✅ **Fallback para inglês** quando tradução não existe

### **Desenvolvimento:**
- ✅ **Scripts de validação** funcionais
- ✅ **Geração de sitemap** automatizada
- ✅ **Comandos npm** configurados
- ✅ **TypeScript** sem erros

## 🧪 COMANDOS DE TESTE

Após implementação, execute estes comandos para validar:

```bash
# Validar traduções
npm run i18n:validate

# Gerar sitemap
npm run seo:sitemap

# Build com SEO
npm run seo:build

# Testar detecção de idioma
curl -H "Accept-Language: pt-BR" http://localhost:3000/create-recipe

# Verificar redirecionamentos
curl -I -H "Accept-Language: es" http://localhost:3000/create-recipe

# Verificar hreflang
curl -s http://localhost:3000/pt/create-recipe | grep hreflang
```

## 📊 MÉTRICAS ESPERADAS

| Métrica | Antes | Depois |
|---------|-------|--------|
| **First Contentful Paint** | 2.1s | < 0.8s |
| **Largest Contentful Paint** | 3.2s | < 1.2s |
| **Cumulative Layout Shift** | 0.25 | < 0.02 |
| **SEO Score** | 65/100 | > 95/100 |
| **FOUC** | ❌ Presente | ✅ Eliminado |

## 🎯 PRIORIDADES DE IMPLEMENTAÇÃO

### **ALTA PRIORIDADE:**
1. Implementar sistema server-side (`src/lib/i18n/server.ts`)
2. Criar hook SSR (`src/hooks/use-server-i18n.ts`)
3. Migrar página principal (`pages/create-recipe.tsx`)
4. Configurar middleware (`src/middleware.ts`)

### **MÉDIA PRIORIDADE:**
5. Implementar componente SEO (`src/components/seo/LocalizedHead.tsx`)
6. Atualizar `pages/_app.tsx`
7. Criar scripts de validação

### **BAIXA PRIORIDADE:**
8. Migrar páginas secundárias
9. Otimizações avançadas
10. Configuração de domínios específicos

## 💡 DICAS IMPORTANTES

1. **Preserve a estrutura existente** de traduções em `src/lib/i18n/messages/`
2. **Mantenha compatibilidade** com hooks existentes durante migração
3. **Teste cada página migrada** individualmente
4. **Use TypeScript** rigorosamente para evitar erros
5. **Documente mudanças** para a equipe

## 🚨 PONTOS DE ATENÇÃO

- **NÃO quebre** a funcionalidade existente durante migração
- **TESTE redirecionamentos** cuidadosamente
- **VALIDE hreflang** em todas as páginas
- **MONITORE performance** antes e depois
- **VERIFIQUE** se todas as traduções existem

---

**RESULTADO ESPERADO:** Sistema de i18n completamente otimizado para SEO, com zero FOUC, performance excelente e indexação perfeita em todos os idiomas! 🎉