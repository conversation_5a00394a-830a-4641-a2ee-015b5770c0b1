/**
 * App component with SSR i18n support
 * Provides translations to all pages without client-side loading
 */

import type { AppProps } from 'next/app';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import type { NamespaceTranslations, SupportedLocale } from '@/lib/i18n';
import '@/styles/globals.css';

interface MyAppProps extends AppProps {
  pageProps: {
    locale?: SupportedLocale;
    translations?: NamespaceTranslations;
    seoMetadata?: any;
    hreflangLinks?: any[];
    [key: string]: any;
  };
}

export default function App({ Component, pageProps }: MyAppProps) {
  const router = useRouter();
  
  // Set HTML lang attribute for accessibility and SEO
  useEffect(() => {
    if (pageProps.locale) {
      document.documentElement.lang = pageProps.locale;
    }
  }, [pageProps.locale]);

  // Add locale-specific CSS classes for styling
  useEffect(() => {
    if (pageProps.locale) {
      document.body.className = `locale-${pageProps.locale}`;
    }
  }, [pageProps.locale]);

  return <Component {...pageProps} />;
}