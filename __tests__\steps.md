Please analyze and study all files inside folder .kiro\specs\server-only-i18n in order to implement what it is proposed.

You also need to study child and parent files to understand dependencies of files and functions and the frontend rendering of inofmration.

During the implementation phase, you need to keep updated this file .kiro\specs\server-only-i18n\i18n-migration-log.md in case another dev continue the work that you are not able to finish.

Before creating or modifying any file, you need to check if exists and their current situation insteadl of assuming any content.

It is already an existing project and some steps were already taken that were not logged, that is way this time we will make it different with logging the steps completed and the next step to be taken.@c:\Users\<USER>\cursor\bedrock-v3/.kiro\specs\server-only-i18n/