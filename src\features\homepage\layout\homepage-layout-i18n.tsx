/**
 * Enhanced homepage layout with server-only i18n integration
 * Provides server-side translations to client components while maintaining all existing optimizations
 */

'use client';

import React from 'react';
import { LoadingProvider } from '@/features/auth/context/loading-context';
import { HeroHeader } from '../components/hero-header/hero-header';
import { HeroContent } from '../components/hero-content/hero-content-i18n';
import { HeroCanvasBackground } from '../components/hero-canvas-background/hero-canvas-background';
import type { SupportedLocale } from '@/types/i18n';

interface HomepageLayoutWithI18nProps {
  locale: SupportedLocale;
  translations: Record<string, Record<string, any>>;
}

/**
 * I18n Context for providing server-side translations to client components
 */
const I18nContext = React.createContext<{
  locale: SupportedLocale;
  translations: Record<string, Record<string, any>>;
  t: (key: string, fallback?: string) => string;
} | null>(null);

/**
 * Hook to use i18n context in client components
 */
export const useServerI18n = () => {
  const context = React.useContext(I18nContext);
  if (!context) {
    throw new Error('useServerI18n must be used within HomepageLayoutWithI18n');
  }
  return context;
};

/**
 * Enhanced homepage layout with server-only i18n support
 * Maintains all existing optimizations while adding translation capabilities
 */
export const HomepageLayoutWithI18n: React.FC<HomepageLayoutWithI18nProps> = ({
  locale,
  translations
}) => {
  // Create translation function for client components
  const t = React.useCallback((key: string, fallback?: string): string => {
    // Handle namespace:key format
    let namespace = 'homepage';
    let translationKey = key;
    
    if (key.includes(':')) {
      const [ns, ...keyParts] = key.split(':');
      namespace = ns;
      translationKey = keyParts.join(':');
    }
    
    // Get nested value using dot notation
    const getNestedValue = (obj: any, path: string): string | undefined => {
      return path.split('.').reduce((current, k) => current?.[k], obj);
    };
    
    const namespaceTranslations = translations[namespace];
    if (!namespaceTranslations) {
      return fallback || key;
    }
    
    const translation = getNestedValue(namespaceTranslations, translationKey);
    return translation || fallback || key;
  }, [translations]);

  const contextValue = React.useMemo(() => ({
    locale,
    translations,
    t
  }), [locale, translations, t]);

  return (
    <I18nContext.Provider value={contextValue}>
      <LoadingProvider>
        <section className="relative bg-background text-muted-foreground min-h-screen flex flex-col overflow-x-hidden pt-[70px]">
          {/* Hero Canvas Background */}
          <HeroCanvasBackground />
          
          {/* Hero Header */}
          <HeroHeader />
          
          {/* Hero Content */}
          <main className="relative z-10 flex-grow flex flex-col items-center justify-center text-center px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20">
            <HeroContent />
          </main>
        </section>
      </LoadingProvider>
    </I18nContext.Provider>
  );
};
