/**
 * Dedicated test page for server-only i18n validation
 * Does not interfere with existing production features
 */

import { createTranslator, getLocale } from '@/lib/i18n/server';
import { Metadata } from 'next';
import Link from 'next/link';

interface I18nTestPageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: I18nTestPageProps): Promise<Metadata> {
  const { locale } = await params;
  
  return {
    title: `Server-Only i18n Test - ${locale.toUpperCase()}`,
    description: 'Dedicated test page for validating server-only internationalization implementation',
  };
}

export default async function I18nTestPage({ params }: I18nTestPageProps) {
  const { locale } = await params;
  const t = await createTranslator('i18n-test');
  const currentLocale = await getLocale();

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold mb-6">
          {t('title')}
        </h1>
        <p className="text-lg text-gray-600 mb-8">
          {t('description')}
        </p>
        
        <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-green-800 mb-3">
            {t('status.title')}
          </h2>
          <p className="text-green-700 mb-4">
            {t('status.message')}
          </p>
          <div className="grid grid-cols-2 gap-4 text-sm text-green-600">
            <div>
              <strong>{t('current-locale')}:</strong> {currentLocale}
            </div>
            <div>
              <strong>{t('timestamp')}:</strong> {new Date().toISOString()}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div className="p-6 border rounded-lg">
            <h3 className="text-lg font-semibold mb-3">
              {t('features.server-only.title')}
            </h3>
            <p className="text-gray-600">
              {t('features.server-only.description')}
            </p>
          </div>
          <div className="p-6 border rounded-lg">
            <h3 className="text-lg font-semibold mb-3">
              {t('features.no-fouc.title')}
            </h3>
            <p className="text-gray-600">
              {t('features.no-fouc.description')}
            </p>
          </div>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <h3 className="text-lg font-semibold text-blue-800 mb-3">
            Test Different Locales
          </h3>
          <div className="flex flex-wrap gap-4">
            <Link 
              href="/en/i18n-test" 
              className={`px-4 py-2 rounded transition-colors ${
                currentLocale === 'en' 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-blue-500 text-white hover:bg-blue-600'
              }`}
            >
              English
            </Link>
            <Link 
              href="/pt/i18n-test" 
              className={`px-4 py-2 rounded transition-colors ${
                currentLocale === 'pt' 
                  ? 'bg-green-600 text-white' 
                  : 'bg-green-500 text-white hover:bg-green-600'
              }`}
            >
              Português
            </Link>
            <Link 
              href="/es/i18n-test" 
              className={`px-4 py-2 rounded transition-colors ${
                currentLocale === 'es' 
                  ? 'bg-red-600 text-white' 
                  : 'bg-red-500 text-white hover:bg-red-600'
              }`}
            >
              Español
            </Link>
          </div>
        </div>

        <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-purple-800 mb-3">
            Additional Test Pages
          </h3>
          <div className="flex flex-wrap gap-4">
            <Link 
              href={`/${currentLocale}/examples`}
              className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
            >
              {t('view-examples')}
            </Link>
            <Link 
              href={`/${currentLocale}`}
              className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
            >
              Back to Home
            </Link>
          </div>
        </div>

        <div className="mt-8 text-sm text-gray-500">
          <p>
            <strong>Note:</strong> This is a test page for the server-only i18n system. 
            It does not interfere with existing production features and can be safely removed after validation.
          </p>
        </div>
      </div>
    </div>
  );
}

export async function generateStaticParams() {
  return [
    { locale: 'en' },
    { locale: 'pt' },
    { locale: 'es' },
  ];
}
