/**
 * TypeScript types for the server-only i18n system
 * Provides type safety for locales, translations, and metadata
 */

export type SupportedLocale = 'en' | 'pt' | 'es';

export interface TranslationNamespace {
  [key: string]: string;
}

export interface TranslationsMap {
  [namespace: string]: TranslationNamespace;
}

export interface LocaleTranslations {
  locale: SupportedLocale;
  translations: TranslationsMap;
}

export interface SEOMetadata {
  title: string;
  description: string;
  keywords?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  canonicalUrl: string;
  locale: SupportedLocale;
  hreflangLinks: Array<{
    hrefLang: string;
    href: string;
  }>;
  structuredData?: any;
}

export interface TranslationProps {
  translations: Record<string, string>;
  locale: SupportedLocale;
}

export interface HreflangLink {
  hrefLang: string;
  href: string;
}

export interface LocalizedPageProps {
  params: Promise<{ locale: string }>;
}

export interface LocalizedLayoutProps {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}

// Translation function type
export type TranslationFunction = (
  key: string,
  params?: Record<string, any>
) => string;

// Namespace-specific translation function
export type NamespaceTranslator = (namespace: string) => Promise<TranslationFunction>;
