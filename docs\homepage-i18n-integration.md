# Homepage i18n Integration

This document explains how the homepage feature has been integrated with the server-only i18n system with user preference support.

## Overview

The homepage now provides a fully localized experience that:
- **Respects user language preferences** stored in the database
- **Maintains SEO optimization** with proper URL-based locale detection
- **Preserves all existing performance optimizations** (LoadingProvider, auth state, animations)
- **Provides seamless user experience** with automatic locale detection and redirection

## Architecture

### URL Structure

- **Root URL (`/`)**: Redirects to user's preferred locale
- **Localized URLs**: `/en`, `/pt`, `/es` - Direct access to specific locales
- **SEO Friendly**: Each locale has its own URL for search engine optimization

### Locale Priority System

1. **URL Locale** (highest priority) - Direct access via `/pt`, `/es`, `/en`
2. **User Database Preference** - Saved in `profiles.language` for authenticated users
3. **Browser Detection** - Accept-Language headers for non-authenticated users
4. **Default Fallback** - English as final fallback

## Implementation Details

### Root Page Redirect (`src/app/page.tsx`)

```tsx
import { redirectToUserPreferredLocale } from '@/lib/i18n/user-preference-redirect';

export default async function RootPage() {
  // Redirect to user's preferred locale (URL → User DB → Browser → Default)
  await redirectToUserPreferredLocale('/');
}
```

**Key Features:**
- Automatic detection of user's preferred locale
- Seamless redirect without user interaction
- Maintains query parameters and fragments

### Localized Homepage (`src/app/[locale]/page.tsx`)

```tsx
export default async function LocalizedHomepage({ params }: LocalizedHomepageProps) {
  const { locale } = await params;
  
  // Get optimal locale (respects user preferences)
  const optimalLocale = await getOptimalLocale(locale);
  
  // Prefetch auth and translation data
  const { user, dehydratedState } = await getServerAuthWithProfilePrefetch({...});
  const { translations } = await getAllTranslationsWithUserPreference(['homepage', 'common'], locale);

  return (
    <HydrationBoundary state={dehydratedState}>
      <HomepageLayoutWithI18n 
        locale={optimalLocale}
        translations={translations}
      />
    </HydrationBoundary>
  );
}
```

**Key Features:**
- Server-side translation prefetching
- User preference integration
- Maintains existing auth state optimizations
- SEO-optimized metadata generation

### Enhanced Homepage Layout (`src/features/homepage/layout/homepage-layout-i18n.tsx`)

```tsx
export const HomepageLayoutWithI18n: React.FC<HomepageLayoutWithI18nProps> = ({
  locale,
  translations
}) => {
  // Create translation function for client components
  const t = React.useCallback((key: string, fallback?: string): string => {
    // Handle namespace:key format and nested dot notation
    // ...
  }, [translations]);

  return (
    <I18nContext.Provider value={{ locale, translations, t }}>
      <LoadingProvider>
        {/* Existing homepage structure with i18n context */}
      </LoadingProvider>
    </I18nContext.Provider>
  );
};
```

**Key Features:**
- Provides server-side translations to client components
- Maintains all existing LoadingProvider optimizations
- Creates translation context for child components
- Preserves existing component structure and animations

### I18n-Enabled Components

#### HeroContent with Server-Side Translations

```tsx
export const HeroContent: React.FC = () => {
  const { t } = useServerI18n();
  
  const rotatingWords = [
    t('hero.rotatingWords.0', 'Support'),
    t('hero.rotatingWords.1', 'Experiences'),
    // ...
  ];

  return (
    <>
      <motion.h1>
        {t('hero.title', 'Deliver collaborative')}<br />
        <RotatingText texts={rotatingWords} />
      </motion.h1>
      {/* All existing animations and functionality preserved */}
    </>
  );
};
```

**Key Features:**
- Uses server-provided translations via context
- Maintains all existing animations and interactivity
- Fallback values for missing translations
- Supports nested translation keys

## User Experience Flows

### Authenticated User with Language Preference

1. User visits `/` (root URL)
2. System detects user is authenticated
3. Retrieves user's language preference from database (e.g., `pt`)
4. Redirects to `/pt` 
5. Portuguese homepage loads with user's auth state immediately available
6. All content displays in Portuguese with no loading states

### Non-Authenticated User

1. User visits `/` (root URL)
2. System detects browser language (e.g., Spanish from Accept-Language)
3. Redirects to `/es`
4. Spanish homepage loads with optimized loading states
5. All content displays in Spanish

### Direct Locale Access (SEO)

1. User visits `/en` directly (from search engine or bookmark)
2. English homepage loads immediately
3. URL locale takes priority over user preference
4. Perfect for SEO and social sharing

### User Preference Mismatch

1. Authenticated user with Portuguese preference visits `/en`
2. English homepage loads (URL takes priority)
3. User sees English content but can navigate to preferred language
4. System respects URL choice for SEO while maintaining user preference for future visits

## Translation Management

### Translation Files Structure

```
src/lib/i18n/messages/
├── en/homepage.json
├── pt/homepage.json
└── es/homepage.json
```

### Translation Keys

```json
{
  "hero": {
    "title": "Deliver collaborative",
    "rotatingWords": {
      "0": "Support",
      "1": "Experiences",
      "2": "Relationships",
      "3": "Help",
      "4": "Service"
    },
    "subtitle": "Support your customers on Slack, Microsoft Teams...",
    "cta": "See Nexus in action",
    "emailPlaceholder": "Your work email"
  }
}
```

## Performance Optimizations

### Server-Side Optimizations

- **React cache()** for translation loading
- **Prefetched auth state** eliminates loading spinners
- **Dehydrated query state** for immediate data availability
- **Optimized locale detection** with minimal database queries

### Client-Side Optimizations

- **Translation context** eliminates prop drilling
- **Memoized translation function** prevents unnecessary re-renders
- **Preserved animations** maintain smooth user experience
- **LoadingProvider integration** ensures consistent loading states

## SEO Benefits

### URL Structure

- **Clean URLs**: `/en`, `/pt`, `/es` for each language
- **Proper hreflang tags** for search engine language detection
- **Localized metadata** with translated titles and descriptions
- **Static generation** for optimal performance

### Content Optimization

- **Server-rendered translations** eliminate FOUC
- **Proper language attributes** on HTML elements
- **Localized content** improves search relevance
- **Fast loading times** with server-side optimizations

## Testing

### Manual Testing

1. **Root Redirect**: Visit `/` and verify redirect to preferred locale
2. **Locale Switching**: Test `/en`, `/pt`, `/es` direct access
3. **User Preferences**: Login and verify database preference is respected
4. **Browser Detection**: Test with different Accept-Language headers
5. **Fallback Behavior**: Test with invalid locales

### Automated Testing

- Translation key validation
- Locale detection logic
- User preference integration
- SEO metadata generation
- Performance benchmarks

## Migration Notes

### Backward Compatibility

- **Existing client-side i18n** continues to work unchanged
- **Original homepage components** remain available
- **API routes** unaffected by homepage changes
- **User profile system** seamlessly integrated

### Breaking Changes

- **Root URL behavior**: Now redirects instead of showing homepage directly
- **Homepage URL structure**: Moved from `/` to `/[locale]`
- **Component imports**: New i18n components available alongside originals

## Troubleshooting

### Common Issues

**Homepage not redirecting:**
- Check `redirectToUserPreferredLocale` implementation
- Verify middleware is running correctly
- Check user profile language field

**Translations not loading:**
- Verify translation files exist for all locales
- Check `getAllTranslationsWithUserPreference` function
- Ensure proper namespace usage in components

**User preferences not working:**
- Check database connection and user profile access
- Verify `getCurrentUserProfile` function
- Ensure user is properly authenticated

**SEO issues:**
- Verify hreflang tags in metadata
- Check URL structure and static generation
- Ensure proper locale detection for crawlers

For additional support, refer to the comprehensive logging throughout the homepage components and the server-only i18n system documentation.
