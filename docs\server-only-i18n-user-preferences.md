# Server-Only i18n with User Preference Integration

This document explains how to use the enhanced server-only internationalization system that integrates with user language preferences stored in the database.

## Overview

The system provides a priority-based locale detection that respects both SEO requirements and user preferences:

1. **URL locale** (highest priority) - `/pt/page`, `/es/page`, `/en/page`
2. **User database preference** - Saved in `profiles.language` field
3. **Browser detection** - From Accept-Language headers
4. **Default fallback** - English (`en`)

## Basic Usage

### Simple Page Translation (URL-based)

For pages where URL locale is sufficient:

```tsx
import { createTranslator, getLocaleFromParams } from '@/lib/i18n/server';

export default async function MyPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const currentLocale = getLocaleFromParams(locale);
  const t = await createTranslator('my-namespace', currentLocale);

  return (
    <div>
      <h1>{t('title')}</h1>
      <p>{t('description')}</p>
    </div>
  );
}
```

### Enhanced Page Translation (with User Preferences)

For pages that should respect user preferences:

```tsx
import { 
  createTranslatorWithUserPreference, 
  getOptimalLocale 
} from '@/lib/i18n/server';

export default async function MyPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  
  // This will use URL locale OR user's database preference
  const optimalLocale = await getOptimalLocale(locale);
  const t = await createTranslatorWithUserPreference('my-namespace', locale);

  return (
    <div>
      <h1>{t('title')}</h1>
      <p>Current locale: {optimalLocale}</p>
    </div>
  );
}
```

## API Reference

### Core Functions

#### `getOptimalLocale(urlLocale?: string): Promise<SupportedLocale>`
Returns the best locale for the current user based on priority system.

#### `createTranslatorWithUserPreference(namespace: string, urlLocale?: string)`
Creates a translator function that respects user preferences.

#### `getAllTranslationsWithUserPreference(namespaces: string[], urlLocale?: string)`
Gets multiple translation namespaces with user preference integration.

### Redirect Utilities

#### `redirectToUserPreferredLocale(requestedPath?: string, fallbackLocale?: SupportedLocale)`
Redirects user to their preferred locale. Use in root pages without locale.

```tsx
// In app/page.tsx (root page without locale)
import { redirectToUserPreferredLocale } from '@/lib/i18n/user-preference-redirect';

export default async function RootPage() {
  await redirectToUserPreferredLocale('/');
}
```

#### `getUserPreferredLocale(): Promise<SupportedLocale>`
Gets user's preferred locale without redirecting.

## User Preference System

### How User Preferences Work

1. **Database Storage**: User language preference is stored in `profiles.language` field
2. **Authentication Required**: Only works for authenticated users
3. **Fallback Graceful**: Falls back to browser detection for non-authenticated users
4. **URL Priority**: URL locale always takes precedence for SEO

### Setting User Language Preference

User language preferences are managed through the existing user profile system:

```tsx
// User can update their language preference
await updateUserProfile(userId, {
  language: 'pt' // or 'en', 'es'
});
```

### Checking User Preference

```tsx
import { getCurrentUserProfile } from '@/features/user-auth-data/services/profile.service';
import { getServerAuthState } from '@/features/auth/services/auth-state.service';

// In a server component
const { user } = await getServerAuthState();
if (user?.id) {
  const profile = await getCurrentUserProfile(user.id);
  console.log('User preferred language:', profile?.language);
}
```

## Migration Guide

### From Basic Server-Only i18n

**Before:**
```tsx
const t = await createTranslator('namespace', currentLocale);
```

**After (with user preferences):**
```tsx
const t = await createTranslatorWithUserPreference('namespace', locale);
```

### From Client-Side i18n

**Before:**
```tsx
'use client';
import { useI18n } from '@/hooks/use-i18n';

function MyComponent() {
  const { t } = useI18n();
  return <div>{t('key')}</div>;
}
```

**After (server-side with user preferences):**
```tsx
// Server Component
import { createTranslatorWithUserPreference } from '@/lib/i18n/server';

async function MyComponent({ locale }: { locale: string }) {
  const t = await createTranslatorWithUserPreference('namespace', locale);
  return <div>{t('key')}</div>;
}
```

## Best Practices

### 1. Use URL Locale for SEO-Critical Pages

For landing pages, marketing pages, and public content:

```tsx
// Always use URL locale for SEO
const currentLocale = getLocaleFromParams(locale);
const t = await createTranslator('namespace', currentLocale);
```

### 2. Use User Preferences for App Pages

For authenticated user dashboards and personalized content:

```tsx
// Respect user preferences for better UX
const t = await createTranslatorWithUserPreference('namespace', locale);
```

### 3. Handle Non-Authenticated Users

The system gracefully handles non-authenticated users:

```tsx
// This works for both authenticated and non-authenticated users
const optimalLocale = await getOptimalLocale(locale);
```

### 4. Root Page Redirects

For pages without locale in URL, redirect to user's preferred locale:

```tsx
// app/page.tsx
export default async function RootPage() {
  await redirectToUserPreferredLocale('/');
}
```

## Compatibility

### With Existing Client-Side System

The enhanced server-only system is fully compatible with the existing client-side `useI18n()` hook:

- **Server Components**: Use `createTranslatorWithUserPreference()`
- **Client Components**: Continue using `useI18n()` hook
- **User Preferences**: Both systems read from the same database field

### With Existing Middleware

The system works with the existing middleware and enhances it with user preference detection.

## Debugging

Enable debug logging in development:

```tsx
// The system automatically logs locale detection in development
// Check console for messages like:
// [i18n] Using URL locale: pt
// [i18n] Using user preference from database: es
// [i18n] Using header-based locale: en
```

## Performance

- **React cache()**: All functions use React cache for optimal performance
- **Single Database Query**: User profile is cached across the request
- **No Client-Side Loading**: Translations are available immediately on server render
- **SEO Optimized**: Search engines see fully translated content

## Testing

Test the integration at `/[locale]/user-preference-test` to see how different scenarios work:

- Authenticated vs non-authenticated users
- URL locale vs user database preference
- Fallback behavior
- Priority system demonstration
