/**
 * SEO-optimized Head component with internationalization support
 * Handles hreflang, localized metadata, and structured data
 */

import Head from 'next/head';
import { useRouter } from 'next/router';

interface LocalizedHeadProps {
  title: string;
  description: string;
  keywords?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  hreflangLinks?: Array<{
    hrefLang: string;
    href: string;
  }>;
  structuredData?: object;
  noIndex?: boolean;
}

export function LocalizedHead({
  title,
  description,
  keywords,
  ogTitle,
  ogDescription,
  ogImage,
  hreflangLinks = [],
  structuredData,
  noIndex = false,
}: LocalizedHeadProps) {
  const router = useRouter();
  const { locale, asPath } = router;
  
  const canonicalUrl = `${process.env.NEXT_PUBLIC_BASE_URL}${locale !== 'en' ? `/${locale}` : ''}${asPath}`;
  const defaultOgImage = `${process.env.NEXT_PUBLIC_BASE_URL}/images/og-default.jpg`;

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{title}</title>
      <meta name="description" content={description} />
      {keywords && <meta name="keywords" content={keywords} />}
      
      {/* Canonical URL */}
      <link rel="canonical" href={canonicalUrl} />
      
      {/* Hreflang Links for International SEO */}
      {hreflangLinks.map(({ hrefLang, href }) => (
        <link
          key={hrefLang}
          rel="alternate"
          hrefLang={hrefLang}
          href={href}
        />
      ))}
      
      {/* Open Graph */}
      <meta property="og:title" content={ogTitle || title} />
      <meta property="og:description" content={ogDescription || description} />
      <meta property="og:image" content={ogImage || defaultOgImage} />
      <meta property="og:url" content={canonicalUrl} />
      <meta property="og:type" content="website" />
      <meta property="og:locale" content={locale === 'pt' ? 'pt_BR' : locale === 'es' ? 'es_ES' : 'en_US'} />
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={ogTitle || title} />
      <meta name="twitter:description" content={ogDescription || description} />
      <meta name="twitter:image" content={ogImage || defaultOgImage} />
      
      {/* Language and Region */}
      <meta httpEquiv="content-language" content={locale} />
      
      {/* Robots */}
      {noIndex && <meta name="robots" content="noindex,nofollow" />}
      
      {/* Structured Data */}
      {structuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData),
          }}
        />
      )}
    </Head>
  );
}