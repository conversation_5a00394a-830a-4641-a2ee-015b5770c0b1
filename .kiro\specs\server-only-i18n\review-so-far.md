PS C:\Users\<USER>\cursor\bedrock-v3> npm run i18n:validate-server

> nextn@0.1.0 i18n:validate-server    
> node scripts/validate-server-i18n.js

🔍 Validating server-only i18n implementation...

📁 Checking translation files...
   Found 5 namespaces: auth, common, create-recipe, dashboard, homepage
   Checking locale: en
⚠️  Incomplete translations in en/create-recipe.json:
     - steps.final-recipes.overview.protocolSummary.drops
     - streaming.found
     - streaming.terminal.streaming
   Checking locale: pt
   Checking locale: es

🏗️  Checking App Router structure...
✅ [locale] directory found
✅ Found [locale]/layout.tsx
✅ Found [locale]/page.tsx
⚠️  Pages Router directory still exists - consider removing after migration

⚙️  Checking server-only utilities...
✅ Found import: cache
✅ Found import: headers
✅ Found cached function: getLocale
✅ Found cached function: getTranslations
✅ Found cached function: createTranslator
✅ Found cached function: generateSEOMetadata

🛡️  Checking middleware configuration...
✅ Found header: x-locale
✅ Found header: x-original-path
✅ Found locale detection

⚙️  Checking Next.js configuration...
✅ No Pages Router i18n config found

🔍 Checking SEO components...
⚠️  Found next/head import - should use Metadata API for App Router
✅ Found Metadata API usage

📊 Validation Summary:
⚠️  Validation completed with warnings
PS C:\Users\<USER>\cursor\bedrock-v3> 


# /es/examples

[i18n] Missing translation file for en/examples
[i18n] Missing translation key: button.text in namespace: examples
[i18n] Missing translation key: button.loading in namespace: examples
[i18n] Missing translation key: locale-switcher.select-language in namespace: examples
[i18n] Missing translation key: locale-switcher.english in namespace: examples
[i18n] Missing translation key: locale-switcher.portuguese in namespace: examples
[i18n] Missing translation key: locale-switcher.spanish in namespace: examples
[i18n] Missing translation key: form.name-label in namespace: examples
[i18n] Missing translation key: form.name-placeholder in namespace: examples
[i18n] Missing translation key: form.name-required in namespace: examples
[i18n] Missing translation key: form.email-label in namespace: examples
[i18n] Missing translation key: form.email-placeholder in namespace: examples
[i18n] Missing translation key: form.email-required in namespace: examples
[i18n] Missing translation key: form.email-invalid in namespace: examples
[i18n] Missing translation key: form.message-label in namespace: examples
[i18n] Missing translation key: form.message-placeholder in namespace: examples
[i18n] Missing translation key: form.message-required in namespace: examples
[i18n] Missing translation key: form.submit-button in namespace: examples
[i18n] Missing translation key: form.submitting in namespace: examples
[i18n] Missing translation key: form.submit-error in namespace: examples
[i18n] Missing translation key: title in namespace: examples
[i18n] Missing translation key: description in namespace: examples
[i18n] Missing translation key: examples.client-button.title in namespace: examples
[i18n] Missing translation key: examples.client-button.description in namespace: examples
[i18n] Missing translation key: examples.client-button.code-title in namespace: examples
[i18n] Missing translation key: examples.locale-switcher.title in namespace: examples
[i18n] Missing translation key: examples.locale-switcher.description in namespace: examples
[i18n] Missing translation key: examples.locale-switcher.code-title in namespace: examples
[i18n] Missing translation key: examples.interactive-form.title in namespace: examples
[i18n] Missing translation key: examples.interactive-form.description in namespace: examples
[i18n] Missing translation key: examples.interactive-form.code-title in namespace: examples
[i18n] Missing translation key: benefits.title in namespace: examples
[i18n] Missing translation key: benefits.no-fouc.title in namespace: examples
[i18n] Missing translation key: benefits.no-fouc.description in namespace: examples
[i18n] Missing translation key: benefits.performance.title in namespace: examples
[i18n] Missing translation key: benefits.performance.description in namespace: examples
[i18n] Missing translation key: benefits.seo.title in namespace: examples
[i18n] Missing translation key: benefits.seo.description in namespace: examples
[i18n] Missing translation key: benefits.hydration.title in namespace: examples
[i18n] Missing translation key: benefits.hydration.description in namespace: examples
 ⨯ Error: Event handlers cannot be passed to Client Component props.
  <... translations={{...}} locale="en" onClick={function onClick} variant=...>
                                                ^^^^^^^^^^^^^^^^^^
If you need interactivity, consider converting part of this to a Client Component.
    at stringify (<anonymous>) {
  digest: '3389991133'
}
[i18n] Missing translation file for en/examples
 GET /es/examples 500 in 1652ms
 GET /favicon.ico?favicon.56766c03.ico 200 in 478ms
 GET /favicon.ico 200 in 456ms
[2025-07-17T17:02:56.193Z] WinstonConfig: Sentry transport for Winston added for levels: warn, error.

Error: [ Server ] [i18n] Missing translation file for en/examples
    at createConsoleError (http://localhost:9002/_next/static/chunks/node_modules_next_dist_client_b1e29c8f._.js:2265:71)
    at handleConsoleError (http://localhost:9002/_next/static/chunks/node_modules_next_dist_client_b1e29c8f._.js:2441:54)
    at console.error (http://localhost:9002/_next/static/chunks/node_modules_next_dist_client_b1e29c8f._.js:2606:57)
    at <anonymous> (rsc://React/Server/C:%5CUsers%5CVistos%5Ccursor%5Cbedrock-v3%5C.next%5Cserver%5Cchunks%5Cssr%5C_1f7fadc3._.js?0:203:17)
    at ExamplesPage (<anonymous>)

# /pt/create-recipe

Error: A tree hydrated but some attributes of the server rendered HTML didn't match the client properties. This won't be patched up. This can happen if a SSR-ed Client Component used:

- A server/client branch `if (typeof window !== 'undefined')`.
- Variable input such as `Date.now()` or `Math.random()` which changes each time it's called.
- Date formatting in a user's locale which doesn't match the server.
- External changing data without sending a snapshot of it along with the HTML.
- Invalid HTML tag nesting.

It can also happen if the client has a browser extension installed which messes with the HTML before React loaded.

https://react.dev/link/hydration-mismatch

  ...
    <HotReload assetPrefix="" globalError={[...]}>
      <AppDevOverlay state={{nextId:1, ...}} globalError={[...]}>
        <AppDevOverlayErrorBoundary globalError={[...]} onError={function bound dispatchSetState}>
          <ReplaySsrOnlyErrors>
          <DevRootHTTPAccessFallbackBoundary>
            <HTTPAccessFallbackBoundary notFound={<NotAllowedRootHTTPFallbackError>}>
              <HTTPAccessFallbackErrorBoundary pathname="/pt/create..." notFound={<NotAllowedRootHTTPFallbackError>} ...>
                <RedirectBoundary>
                  <RedirectErrorBoundary router={{...}}>
                    <Head>
                    <link>
                    <script>
                    <script>
                    <script>
                    <script>
                    <RootLayout>
                      <html lang="en" suppressHydrationWarning={true}>
                        <body
                          className="inter_9e72d27f-module__JKMi0a__variable font-sans antialiased"
-                         cz-shortcut-listen="true"
                        >
                          ...
                            <InnerLayoutRouter url="/pt/create..." tree={[...]} cacheNode={{lazyData:null, ...}} ...>
                              <LocaleLayout>
                                <html
+                                 lang="pt"
-                                 lang="en"
-                                 data-google-analytics-opt-out=""
-                                 className="light"
-                                 style={{color-scheme:"light"}}
                                >
                                  <body
+                                   className="locale-pt"
-                                   className="inter_9e72d27f-module__JKMi0a__variable font-sans antialiased"
-                                   cz-shortcut-listen="true"
                                  >
                    ...
        ...

    at createConsoleError (http://localhost:9002/_next/static/chunks/node_modules_next_dist_client_b1e29c8f._.js:2265:71)
    at handleConsoleError (http://localhost:9002/_next/static/chunks/node_modules_next_dist_client_b1e29c8f._.js:2441:54)
    at console.error (http://localhost:9002/_next/static/chunks/node_modules_next_dist_client_b1e29c8f._.js:2606:57)
    at http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_dae1c9ab._.js:5783:25
    at runWithFiberInDEV (http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_dae1c9ab._.js:3703:74)
    at emitPendingHydrationWarnings (http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_dae1c9ab._.js:5782:13)
    at completeWork (http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_dae1c9ab._.js:8961:102)
    at runWithFiberInDEV (http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_dae1c9ab._.js:3703:131)
    at completeUnitOfWork (http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_dae1c9ab._.js:10938:23)
    at performUnitOfWork (http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_dae1c9ab._.js:10875:28)
    at workLoopConcurrentByScheduler (http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_dae1c9ab._.js:10869:58)
    at renderRootConcurrent (http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_dae1c9ab._.js:10851:71)
    at performWorkOnRoot (http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_dae1c9ab._.js:10483:176)
    at performWorkOnRootViaSchedulerTask (http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_dae1c9ab._.js:11456:9)
    at MessagePort.performWorkUntilDeadline (http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_dae1c9ab._.js:2826:64)
    at body (<anonymous>)
    at LocaleLayout (rsc://React/Server/C:%5CUsers%5CVistos%5Ccursor%5Cbedrock-v3%5C.next%5Cserver%5Cchunks%5Cssr%5C_3995f043._.js?36:34:270)

[i18n] Missing translation key: steps.demographics.title in namespace: create-recipe
[i18n] Missing translation key: steps.health-concerns.title in namespace: create-recipe
[i18n] Missing translation key: steps.symptoms.title in namespace: create-recipe
[i18n] Missing translation key: steps.demographics.title in namespace: create-recipe
[i18n] Missing translation key: steps.demographics.fields.age.label in namespace: create-recipe
[i18n] Missing translation key: steps.demographics.fields.age.placeholder in namespace: create-recipe
[i18n] Missing translation key: steps.demographics.fields.age.options.55plus in namespace: create-recipe
[i18n] Missing translation key: steps.demographics.fields.gender.label in namespace: create-recipe
[i18n] Missing translation key: steps.demographics.fields.gender.placeholder in namespace: create-recipe
[i18n] Missing translation key: steps.demographics.fields.gender.options.male in namespace: create-recipe
[i18n] Missing translation key: steps.demographics.fields.gender.options.female in namespace: create-recipe
[i18n] Missing translation key: steps.demographics.fields.language.label in namespace: create-recipe
[i18n] Missing translation key: steps.demographics.fields.language.placeholder in namespace: create-recipe
[i18n] Missing translation key: steps.demographics.fields.language.options.english in namespace: create-recipe
[i18n] Missing translation key: steps.demographics.fields.language.options.portuguese in namespace: create-recipe
[i18n] Missing translation key: steps.demographics.fields.language.options.spanish in namespace: create-recipe
[i18n] Missing translation key: steps.demographics.fields.experience.label in namespace: create-recipe
[i18n] Missing translation key: steps.demographics.fields.experience.placeholder in namespace: create-recipe
[i18n] Missing translation key: steps.demographics.fields.experience.options.beginner in namespace: create-recipe
[i18n] Missing translation key: steps.demographics.fields.experience.options.intermediate in namespace: create-recipe
[i18n] Missing translation key: steps.demographics.fields.experience.options.advanced in namespace: create-recipe
[i18n] Missing translation key: buttons.continue in namespace: create-recipe
[i18n] Missing translation key: benefits.personalized.title in namespace: create-recipe
[i18n] Missing translation key: benefits.personalized.description in namespace: create-recipe
[i18n] Missing translation key: benefits.expert.title in namespace: create-recipe
[i18n] Missing translation key: benefits.expert.description in namespace: create-recipe
[i18n] Missing translation key: benefits.natural.title in namespace: create-recipe
[i18n] Missing translation key: benefits.natural.description in namespace: create-recipe
 GET /pt/create-recipe 200 in 1535ms


 # /en/

 Error: A tree hydrated but some attributes of the server rendered HTML didn't match the client properties. This won't be patched up. This can happen if a SSR-ed Client Component used:

- A server/client branch `if (typeof window !== 'undefined')`.
- Variable input such as `Date.now()` or `Math.random()` which changes each time it's called.
- Date formatting in a user's locale which doesn't match the server.
- External changing data without sending a snapshot of it along with the HTML.
- Invalid HTML tag nesting.

It can also happen if the client has a browser extension installed which messes with the HTML before React loaded.

https://react.dev/link/hydration-mismatch

  ...
    <OuterLayoutRouter parallelRouterKey="children" template={<RenderFromTemplateContext>} notFound={[...]}>
      <RenderFromTemplateContext>
        <ScrollAndFocusHandler segmentPath={[...]}>
          <InnerScrollAndFocusHandler segmentPath={[...]} focusAndScrollRef={{apply:false, ...}}>
            <ErrorBoundary errorComponent={undefined} errorStyles={undefined} errorScripts={undefined}>
              <LoadingBoundary loading={null}>
                <HTTPAccessFallbackBoundary notFound={[...]} forbidden={undefined} unauthorized={undefined}>
                  <HTTPAccessFallbackErrorBoundary pathname="/en" notFound={[...]} forbidden={undefined} ...>
                    <RedirectBoundary>
                      <RedirectErrorBoundary router={{...}}>
                        <InnerLayoutRouter url="/en" tree={[...]} cacheNode={{lazyData:null, ...}} segmentPath={[...]}>
                          <LocaleLayout>
                            <html
                              lang="en"
-                             data-google-analytics-opt-out=""
-                             className="light"
-                             style={{color-scheme:"light"}}
                            >
                              <body
+                               className="locale-en"
-                               className="inter_9e72d27f-module__JKMi0a__variable font-sans antialiased"
                              >
    ...

    at createConsoleError (http://localhost:9002/_next/static/chunks/node_modules_next_dist_client_b1e29c8f._.js:2265:71)
    at handleConsoleError (http://localhost:9002/_next/static/chunks/node_modules_next_dist_client_b1e29c8f._.js:2441:54)
    at console.error (http://localhost:9002/_next/static/chunks/node_modules_next_dist_client_b1e29c8f._.js:2606:57)
    at http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_dae1c9ab._.js:5783:25
    at runWithFiberInDEV (http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_dae1c9ab._.js:3703:74)
    at emitPendingHydrationWarnings (http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_dae1c9ab._.js:5782:13)
    at completeWork (http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_dae1c9ab._.js:8961:102)
    at runWithFiberInDEV (http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_dae1c9ab._.js:3703:131)
    at completeUnitOfWork (http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_dae1c9ab._.js:10938:23)
    at performUnitOfWork (http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_dae1c9ab._.js:10875:28)
    at workLoopConcurrentByScheduler (http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_dae1c9ab._.js:10869:58)
    at renderRootConcurrent (http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_dae1c9ab._.js:10851:71)
    at performWorkOnRoot (http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_dae1c9ab._.js:10483:176)
    at performWorkOnRootViaSchedulerTask (http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_dae1c9ab._.js:11456:9)
    at MessagePort.performWorkUntilDeadline (http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_dae1c9ab._.js:2826:64)
    at body (<anonymous>)
    at LocaleLayout (rsc://React/Server/C:%5CUsers%5CVistos%5Ccursor%5Cbedrock-v3%5C.next%5Cserver%5Cchunks%5Cssr%5C_3995f043._.js?16:34:270)

    [i18n] Missing translation key: hero.title in namespace: homepage
[i18n] Missing translation key: hero.description in namespace: homepage
[i18n] Missing translation key: features.personalized.title in namespace: homepage
[i18n] Missing translation key: features.personalized.description in namespace: homepage
[i18n] Missing translation key: features.expert.title in namespace: homepage
[i18n] Missing translation key: features.expert.description in namespace: homepage
[i18n] Missing translation key: features.natural.title in namespace: homepage
[i18n] Missing translation key: features.natural.description in namespace: homepage
 GET /en 200 in 700ms