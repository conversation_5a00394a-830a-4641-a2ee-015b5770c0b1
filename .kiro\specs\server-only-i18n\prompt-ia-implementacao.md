# 🤖 Prompt para IA - Implementar i18n SEO-Otimizada

## CONTEXTO
Projeto Next.js com problema crítico: site carrega chaves de tradução antes do texto real, causando FOUC e prejudicando SEO. Preciso implementar SSR i18n para eliminar esse problema.

## ARQUIVOS DE REFERÊNCIA DISPONÍVEIS
Tenho uma implementação completa já criada nos seguintes arquivos:

**Core System:**
- `src/lib/i18n/server.ts` - Sistema server-side completo
- `src/hooks/use-server-i18n.ts` - Hook otimizado para SSR  
- `src/middleware.ts` - Detecção automática de idioma
- `src/components/seo/LocalizedHead.tsx` - Componente SEO internacional

**Configuração:**
- `next.config.ts` - Já configurado com i18n
- `package.json` - Scripts de SEO adicionados

**Exemplos:**
- `pages/_app.tsx` - App component com SSR i18n
- `pages/create-recipe.tsx` - P<PERSON>gina exemplo otimizada

**Scripts:**
- `scripts/generate-sitemap.js` - Sitemap multilíngue
- `scripts/validate-translations.js` - Validação de traduções

**Documentação:**
- `docs/i18n-seo-implementation-guide.md` - Guia completo

## TAREFA
Implemente esta solução no projeto seguindo exatamente os padrões dos arquivos de referência:

### 1. **Copie/implemente os arquivos core** exatamente como estão
### 2. **Migre as páginas existentes** do padrão antigo para o novo:

**ANTES (problemático):**
```tsx
const { t, isLoading } = useI18n();
if (isLoading) return <div>Loading...</div>; // FOUC!
```

**DEPOIS (SEO-otimizado):**
```tsx
const { t } = useServerI18n({ locale, translations });
// + getServerSideProps com getI18nServerSideProps
```

### 3. **Execute os comandos de validação:**
```bash
npm run i18n:validate
npm run seo:sitemap  
npm run seo:build
```

## RESULTADO ESPERADO
- ✅ Zero FOUC - conteúdo instantâneo
- ✅ SEO perfeito - crawlers veem texto real
- ✅ Hreflang correto em todas as páginas
- ✅ Performance otimizada (FCP < 1s, CLS < 0.1)
- ✅ URLs localizadas (/pt/, /es/, /en/)

## PRIORIDADE
1. Implementar sistema server-side
2. Migrar página `create-recipe` primeiro
3. Testar funcionamento
4. Migrar outras páginas críticas

**Use os arquivos de referência como base exata - eles contêm a solução completa e testada!**