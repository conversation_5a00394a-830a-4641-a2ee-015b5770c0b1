/**
 * Locale-specific layout for App Router
 * Provides locale validation and context without conflicting with root layout
 */

import { notFound } from 'next/navigation';

const locales = ['en', 'pt', 'es'];

interface LocaleLayoutProps {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}

export default async function LocaleLayout({
  children,
  params,
}: LocaleLayoutProps) {
  const { locale } = await params;

  // Validate locale
  if (!locales.includes(locale)) {
    notFound();
  }

  // Return children wrapped in a div with locale class for styling
  // The html lang attribute will be handled by the root layout
  return (
    <div className={`locale-${locale}`} data-locale={locale}>
      {children}
    </div>
  );
}

export async function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}
