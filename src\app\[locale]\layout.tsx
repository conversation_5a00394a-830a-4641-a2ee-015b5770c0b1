/**
 * Locale-specific layout for App Router
 * Provides server-side translations and SEO optimization
 */

import { notFound } from 'next/navigation';
import { getLocale } from '@/lib/i18n/server';

const locales = ['en', 'pt', 'es'];

interface LocaleLayoutProps {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}

export default async function LocaleLayout({
  children,
  params,
}: LocaleLayoutProps) {
  const { locale } = await params;
  
  // Validate locale
  if (!locales.includes(locale)) {
    notFound();
  }

  // Set HTML lang attribute for accessibility and SEO
  return (
    <html lang={locale}>
      <body className={`locale-${locale}`}>
        {children}
      </body>
    </html>
  );
}

export async function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}
