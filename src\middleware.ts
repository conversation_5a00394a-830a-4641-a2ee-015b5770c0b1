/**
 * Next.js Middleware for i18n routing and SEO optimization
 * Handles locale detection, redirects, and SEO headers
 */

import { NextRequest, NextResponse } from 'next/server';

const locales = ['en', 'pt', 'es'] as const;
const defaultLocale = 'en';

// Paths that should not be internationalized
const excludedPaths = [
  '/api',
  '/images',
  '/icons',
  '/favicon.ico',
  '/robots.txt',
  '/sitemap.xml',
  '/_next',
  '/monitoring', // Sentry tunnel
];

export function middleware(request: NextRequest) {
  const { pathname, search } = request.nextUrl;

  // Skip excluded paths
  if (excludedPaths.some(path => pathname.startsWith(path))) {
    return NextResponse.next();
  }

  // Check if URL already has a locale
  const pathnameHasLocale = locales.some(
    locale => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  );

  if (pathnameHasLocale) {
    // Extract locale from URL
    const locale = pathname.split('/')[1] as typeof locales[number];

    // Create a response that passes through the request
    const response = NextResponse.next();

    // Set x-locale header for server components
    response.headers.set('x-locale', locale);
    response.headers.set('x-original-path', pathname.substring(locale.length + 1) || '/');

    return response;
  }

  // Detect locale preference and redirect
  const locale = getLocaleFromRequest(request);

  // Redirect to localized URL
  const url = request.nextUrl.clone();
  url.pathname = `/${locale}${pathname === '/' ? '' : pathname}`;
  url.search = search;

  // 302 redirect for SEO
  const response = NextResponse.redirect(url, 302);

  // Set locale cookie
  response.cookies.set('locale', locale, {
    path: '/',
    maxAge: 60 * 60 * 24 * 365, // 1 year
    httpOnly: false,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax'
  });

  return response;
}

/**
 * Detect locale from request headers and cookies
 */
function getLocaleFromRequest(request: NextRequest): string {
  // 1. Check cookie preference first
  const cookieLocale = request.cookies.get('locale')?.value;
  if (cookieLocale && locales.includes(cookieLocale as any)) {
    return cookieLocale;
  }

  // 2. Check Accept-Language header
  const acceptLanguage = request.headers.get('accept-language');
  if (acceptLanguage) {
    const preferredLocale = acceptLanguage
      .split(',')
      .map(lang => {
        const [langCode, weight] = lang.trim().split(';');
        return {
          code: langCode.substring(0, 2),
          weight: weight ? parseFloat(weight.split('=')[1]) : 1.0
        };
      })
      .sort((a, b) => b.weight - a.weight)
      .find(lang => locales.includes(lang.code as any));

    if (preferredLocale) {
      return preferredLocale.code;
    }
  }

  // 3. Default fallback
  return defaultLocale;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - images, icons (static assets)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|images|icons|robots.txt|sitemap.xml|monitoring).*)',
  ],
};