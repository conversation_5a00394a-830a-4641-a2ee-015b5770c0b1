/**
 * Next.js Middleware for i18n routing, auth, and SEO optimization
 * Handles locale detection, redirects, auth session management, and SEO headers
 */

import { NextRequest, NextResponse } from 'next/server';
import { updateSession } from '@/features/auth/utils/middleware.utils';

const locales = ['en', 'pt', 'es'] as const;
const defaultLocale = 'en';

// Paths that should not be internationalized
const excludedPaths = [
  '/api',
  '/images',
  '/icons',
  '/favicon.ico',
  '/robots.txt',
  '/sitemap.xml',
  '/_next',
  '/monitoring', // Sentry tunnel
];

async function middleware(request: NextRequest) {
  console.log('🚀 MIDDLEWARE RUNNING - BASIC TEST');

  const { pathname, search } = request.nextUrl;

  console.log('[middleware] Processing request:', { pathname, search });

  // Skip excluded paths
  if (excludedPaths.some(path => pathname.startsWith(path))) {
    console.log('[middleware] Skipping excluded path:', pathname);
    return NextResponse.next();
  }

  // First, handle auth session management
  let authResponse;
  try {
    console.log('[middleware] Calling auth middleware...');
    authResponse = await updateSession(request);
    console.log('[middleware] Auth middleware completed, status:', authResponse.status);

    // If auth middleware returned a redirect, respect it
    if (authResponse.status === 302 || authResponse.status === 307) {
      console.log('[middleware] Auth middleware returned redirect');
      return authResponse;
    }
  } catch (error) {
    console.error('[middleware] Auth middleware error:', error);
    // Continue with basic response if auth fails
    authResponse = NextResponse.next();
  }

  // Check if URL already has a locale
  const pathnameHasLocale = locales.some(
    locale => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  );

  if (pathnameHasLocale) {
    // Extract locale from URL
    const locale = pathname.split('/')[1] as typeof locales[number];

    // Debug logging
    console.log('[middleware] Setting locale header:', { pathname, locale });

    // Use the auth response and add locale headers
    authResponse.headers.set('x-locale', locale);
    authResponse.headers.set('x-original-path', pathname.substring(locale.length + 1) || '/');

    return authResponse;
  }

  // Detect locale preference and redirect
  const locale = getLocaleFromRequest(request);
  console.log('[middleware] Detected locale for redirect:', locale);

  // Redirect to localized URL
  const url = request.nextUrl.clone();
  url.pathname = `/${locale}${pathname === '/' ? '' : pathname}`;
  url.search = search;

  console.log('[middleware] Redirecting to:', url.pathname);

  // 302 redirect for SEO
  const response = NextResponse.redirect(url, 302);

  // Set locale cookie
  response.cookies.set('locale', locale, {
    path: '/',
    maxAge: 60 * 60 * 24 * 365, // 1 year
    httpOnly: false,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax'
  });

  // Copy any cookies from auth response
  authResponse.cookies.getAll().forEach(cookie => {
    response.cookies.set(cookie.name, cookie.value, {
      path: cookie.path,
      domain: cookie.domain,
      expires: cookie.expires,
      httpOnly: cookie.httpOnly,
      secure: cookie.secure,
      sameSite: cookie.sameSite
    });
  });

  return response;
}

/**
 * Detect locale from request headers and cookies
 * Enhanced version that properly handles browser language detection
 */
function getLocaleFromRequest(request: NextRequest): string {
  // 1. Check cookie preference first
  const cookieLocale = request.cookies.get('locale')?.value;
  if (cookieLocale && locales.includes(cookieLocale as any)) {
    console.log('[middleware] Using cookie locale:', cookieLocale);
    return cookieLocale;
  }

  // 2. Check Accept-Language header with improved parsing
  const acceptLanguage = request.headers.get('accept-language');
  if (acceptLanguage) {
    console.log('[middleware] Parsing Accept-Language:', acceptLanguage);

    try {
      const languages = acceptLanguage
        .split(',')
        .map(lang => {
          const [langCode, weight] = lang.trim().split(';');
          if (!langCode) return null;
          const code = langCode.substring(0, 2).toLowerCase();
          const priority = weight ? parseFloat(weight.split('=')[1] || '1.0') : 1.0;
          return { code, priority };
        })
        .filter((lang): lang is { code: string; priority: number } => lang !== null)
        .sort((a, b) => b.priority - a.priority);

      // Find the first supported language
      for (const lang of languages) {
        if (locales.includes(lang.code as any)) {
          console.log('[middleware] Detected browser locale:', lang.code);
          return lang.code;
        }
      }
    } catch (error) {
      console.log('[middleware] Error parsing Accept-Language:', error);
    }
  }

  // 3. Default fallback
  console.log('[middleware] Using default locale:', defaultLocale);
  return defaultLocale;
}

export default middleware;

export const config = {
  matcher: [
    // Explicitly match root path
    '/',
    // Match all other paths except excluded ones
    '/((?!api|_next/static|_next/image|favicon.ico|images|icons|robots.txt|sitemap.xml|monitoring).*)',
  ],
};