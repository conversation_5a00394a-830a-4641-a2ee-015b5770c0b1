/**
 * MINIMAL TEST MIDDLEWARE - Testing if middleware executes at all
 */

import { NextRequest, NextResponse } from 'next/server';

export default function middleware(request: NextRequest) {
  console.log('🚀🚀🚀 MIDDLEWARE IS RUNNING! 🚀🚀🚀');
  console.log('Request path:', request.nextUrl.pathname);

  // For root path, redirect to /pt to test Portuguese
  if (request.nextUrl.pathname === '/') {
    console.log('🇵🇹 Redirecting to Portuguese!');
    const url = request.nextUrl.clone();
    url.pathname = '/pt';
    return NextResponse.redirect(url);
  }

  // For all other paths, just continue
  return NextResponse.next();
}



export const config = {
  matcher: [
    // Match root path and all other paths except excluded ones
    '/((?!api|_next/static|_next/image|favicon.ico|images|icons|robots.txt|sitemap.xml|monitoring).*)',
  ],
};