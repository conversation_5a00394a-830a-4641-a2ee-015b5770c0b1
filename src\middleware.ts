/**
 * Next.js Middleware for i18n routing and SEO optimization
 * Handles locale detection, redirects, and SEO headers
 */

import { NextRequest, NextResponse } from 'next/server';

const locales = ['en', 'pt', 'es'] as const;
const defaultLocale = 'en';

// Paths that should not be internationalized
const excludedPaths = [
  '/api',
  '/images',
  '/icons',
  '/favicon.ico',
  '/robots.txt',
  '/sitemap.xml',
  '/_next',
  '/monitoring', // Sentry tunnel
];

export function middleware(request: NextRequest) {
  const { pathname, search } = request.nextUrl;

  console.log('[middleware] Processing request:', { pathname, search });

  // Skip excluded paths
  if (excludedPaths.some(path => pathname.startsWith(path))) {
    console.log('[middleware] Skipping excluded path:', pathname);
    return NextResponse.next();
  }

  // Check if URL already has a locale
  const pathnameHasLocale = locales.some(
    locale => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  );

  if (pathnameHasLocale) {
    // Extract locale from URL
    const locale = pathname.split('/')[1] as typeof locales[number];

    // Debug logging
    console.log('[middleware] Setting locale header:', { pathname, locale });

    // Create a response that passes through the request
    const response = NextResponse.next();

    // Set x-locale header for server components
    response.headers.set('x-locale', locale);
    response.headers.set('x-original-path', pathname.substring(locale.length + 1) || '/');

    return response;
  }

  // Detect locale preference and redirect
  const locale = getLocaleFromRequest(request);
  console.log('[middleware] Detected locale for redirect:', locale);

  // Redirect to localized URL
  const url = request.nextUrl.clone();
  url.pathname = `/${locale}${pathname === '/' ? '' : pathname}`;
  url.search = search;

  console.log('[middleware] Redirecting to:', url.pathname);

  // 302 redirect for SEO
  const response = NextResponse.redirect(url, 302);

  // Set locale cookie
  response.cookies.set('locale', locale, {
    path: '/',
    maxAge: 60 * 60 * 24 * 365, // 1 year
    httpOnly: false,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax'
  });

  return response;
}

/**
 * Detect locale from request headers and cookies
 * Enhanced version that properly handles browser language detection
 */
function getLocaleFromRequest(request: NextRequest): string {
  // 1. Check cookie preference first
  const cookieLocale = request.cookies.get('locale')?.value;
  if (cookieLocale && locales.includes(cookieLocale as any)) {
    console.log('[middleware] Using cookie locale:', cookieLocale);
    return cookieLocale;
  }

  // 2. Check Accept-Language header with improved parsing
  const acceptLanguage = request.headers.get('accept-language');
  if (acceptLanguage) {
    console.log('[middleware] Parsing Accept-Language:', acceptLanguage);

    try {
      const languages = acceptLanguage
        .split(',')
        .map(lang => {
          const [langCode, weight] = lang.trim().split(';');
          const code = langCode.substring(0, 2).toLowerCase();
          const priority = weight ? parseFloat(weight.split('=')[1]) : 1.0;
          return { code, priority };
        })
        .sort((a, b) => b.priority - a.priority);

      // Find the first supported language
      for (const lang of languages) {
        if (locales.includes(lang.code as any)) {
          console.log('[middleware] Detected browser locale:', lang.code);
          return lang.code;
        }
      }
    } catch (error) {
      console.log('[middleware] Error parsing Accept-Language:', error);
    }
  }

  // 3. Default fallback
  console.log('[middleware] Using default locale:', defaultLocale);
  return defaultLocale;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - images, icons (static assets)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|images|icons|robots.txt|sitemap.xml|monitoring).*)',
  ],
};