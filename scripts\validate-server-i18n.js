#!/usr/bin/env node

/**
 * Validation script for server-only i18n implementation
 * Checks translation completeness, App Router structure, and SEO compliance
 */

const fs = require('fs');
const path = require('path');

const SUPPORTED_LOCALES = ['en', 'pt', 'es'];
const MESSAGES_DIR = path.join(process.cwd(), 'src', 'lib', 'i18n', 'messages');
const APP_DIR = path.join(process.cwd(), 'src', 'app');

console.log('🔍 Validating server-only i18n implementation...\n');

let hasErrors = false;
let hasWarnings = false;

/**
 * Check if all translation files exist and have consistent keys
 */
function validateTranslationFiles() {
  console.log('📁 Checking translation files...');
  
  if (!fs.existsSync(MESSAGES_DIR)) {
    console.log('❌ Messages directory not found:', MESSAGES_DIR);
    hasErrors = true;
    return;
  }

  // Get all namespaces from English (base language)
  const enDir = path.join(MESSAGES_DIR, 'en');
  if (!fs.existsSync(enDir)) {
    console.log('❌ English translation directory not found:', enDir);
    hasErrors = true;
    return;
  }

  const namespaces = fs.readdirSync(enDir)
    .filter(file => file.endsWith('.json'))
    .map(file => file.replace('.json', ''));

  console.log(`   Found ${namespaces.length} namespaces: ${namespaces.join(', ')}`);

  // Check each locale
  for (const locale of SUPPORTED_LOCALES) {
    const localeDir = path.join(MESSAGES_DIR, locale);
    
    if (!fs.existsSync(localeDir)) {
      console.log(`❌ Missing locale directory: ${locale}`);
      hasErrors = true;
      continue;
    }

    console.log(`   Checking locale: ${locale}`);
    
    // Check each namespace
    for (const namespace of namespaces) {
      const filePath = path.join(localeDir, `${namespace}.json`);
      
      if (!fs.existsSync(filePath)) {
        console.log(`❌ Missing translation file: ${locale}/${namespace}.json`);
        hasErrors = true;
        continue;
      }

      try {
        const content = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        
        // Check for TRANSLATE prefixes (incomplete translations)
        const incompleteKeys = findIncompleteTranslations(content);
        if (incompleteKeys.length > 0) {
          console.log(`⚠️  Incomplete translations in ${locale}/${namespace}.json:`);
          incompleteKeys.forEach(key => console.log(`     - ${key}`));
          hasWarnings = true;
        }
        
      } catch (error) {
        console.log(`❌ Invalid JSON in ${locale}/${namespace}.json: ${error.message}`);
        hasErrors = true;
      }
    }
  }
}

/**
 * Find keys with TRANSLATE prefixes or untranslated content
 */
function findIncompleteTranslations(obj, prefix = '') {
  const incomplete = [];
  
  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    
    if (typeof value === 'string') {
      if (value.startsWith('TRANSLATE:') || value === key) {
        incomplete.push(fullKey);
      }
    } else if (typeof value === 'object' && value !== null) {
      incomplete.push(...findIncompleteTranslations(value, fullKey));
    }
  }
  
  return incomplete;
}

/**
 * Check App Router structure for [locale] dynamic routes
 */
function validateAppRouterStructure() {
  console.log('\n🏗️  Checking App Router structure...');
  
  const localeDir = path.join(APP_DIR, '[locale]');
  
  if (!fs.existsSync(localeDir)) {
    console.log('❌ [locale] dynamic route directory not found:', localeDir);
    hasErrors = true;
    return;
  }

  console.log('✅ [locale] directory found');

  // Check for required files
  const requiredFiles = ['layout.tsx', 'page.tsx'];
  
  for (const file of requiredFiles) {
    const filePath = path.join(localeDir, file);
    if (!fs.existsSync(filePath)) {
      console.log(`❌ Missing required file: [locale]/${file}`);
      hasErrors = true;
    } else {
      console.log(`✅ Found [locale]/${file}`);
    }
  }

  // Check for old Pages Router files
  const pagesDir = path.join(process.cwd(), 'pages');
  if (fs.existsSync(pagesDir)) {
    console.log('⚠️  Pages Router directory still exists - consider removing after migration');
    hasWarnings = true;
  }
}

/**
 * Check server-only i18n utilities
 */
function validateServerUtilities() {
  console.log('\n⚙️  Checking server-only utilities...');
  
  const serverFile = path.join(process.cwd(), 'src', 'lib', 'i18n', 'server.ts');
  
  if (!fs.existsSync(serverFile)) {
    console.log('❌ Server utilities file not found:', serverFile);
    hasErrors = true;
    return;
  }

  const content = fs.readFileSync(serverFile, 'utf8');
  
  // Check for required imports and functions
  const requiredImports = ['cache', 'headers'];
  const requiredFunctions = ['getLocale', 'getTranslations', 'createTranslator', 'generateSEOMetadata'];
  
  for (const imp of requiredImports) {
    if (!content.includes(imp)) {
      console.log(`❌ Missing required import: ${imp}`);
      hasErrors = true;
    } else {
      console.log(`✅ Found import: ${imp}`);
    }
  }

  for (const func of requiredFunctions) {
    if (!content.includes(`export const ${func} = cache(`)) {
      console.log(`❌ Missing or incorrectly implemented function: ${func}`);
      hasErrors = true;
    } else {
      console.log(`✅ Found cached function: ${func}`);
    }
  }

  // Check for Pages Router patterns (should not exist)
  const pagesRouterPatterns = ['GetServerSidePropsContext', 'GetStaticPropsContext', 'useRouter'];

  for (const pattern of pagesRouterPatterns) {
    if (content.includes(pattern)) {
      console.log(`⚠️  Found Pages Router pattern in server utilities: ${pattern}`);
      hasWarnings = true;
    }
  }

  // Check for hydration safety
  if (content.includes('suppressHydrationWarning')) {
    console.log('✅ Found hydration safety measures');
  } else {
    console.log('⚠️  No hydration safety measures found');
    hasWarnings = true;
  }
}

/**
 * Check middleware configuration
 */
function validateMiddleware() {
  console.log('\n🛡️  Checking middleware configuration...');
  
  const middlewareFile = path.join(process.cwd(), 'src', 'middleware.ts');
  
  if (!fs.existsSync(middlewareFile)) {
    console.log('❌ Middleware file not found:', middlewareFile);
    hasErrors = true;
    return;
  }

  const content = fs.readFileSync(middlewareFile, 'utf8');
  
  // Check for required headers
  const requiredHeaders = ['x-locale', 'x-original-path'];
  
  for (const header of requiredHeaders) {
    if (!content.includes(header)) {
      console.log(`❌ Missing required header in middleware: ${header}`);
      hasErrors = true;
    } else {
      console.log(`✅ Found header: ${header}`);
    }
  }

  // Check for locale detection
  if (!content.includes('getLocaleFromRequest')) {
    console.log('❌ Missing locale detection function');
    hasErrors = true;
  } else {
    console.log('✅ Found locale detection');
  }

  // Check for LocaleSetter component
  const localeSetterFile = path.join(process.cwd(), 'src', 'components', 'locale-setter.tsx');
  if (fs.existsSync(localeSetterFile)) {
    console.log('✅ Found LocaleSetter component for hydration safety');
  } else {
    console.log('⚠️  LocaleSetter component not found');
    hasWarnings = true;
  }
}

/**
 * Check Next.js configuration
 */
function validateNextConfig() {
  console.log('\n⚙️  Checking Next.js configuration...');
  
  const configFile = path.join(process.cwd(), 'next.config.ts');
  
  if (!fs.existsSync(configFile)) {
    console.log('❌ Next.js config file not found:', configFile);
    hasErrors = true;
    return;
  }

  const content = fs.readFileSync(configFile, 'utf8');
  
  // Check that Pages Router i18n config is removed
  if (content.includes('i18n:')) {
    console.log('⚠️  Found i18n configuration in next.config.ts - should be removed for App Router');
    hasWarnings = true;
  } else {
    console.log('✅ No Pages Router i18n config found');
  }
}

/**
 * Check for SEO components
 */
function validateSEOComponents() {
  console.log('\n🔍 Checking SEO components...');
  
  const seoFile = path.join(process.cwd(), 'src', 'components', 'seo', 'localized-head.tsx');
  
  if (!fs.existsSync(seoFile)) {
    console.log('❌ SEO component file not found:', seoFile);
    hasErrors = true;
    return;
  }

  const content = fs.readFileSync(seoFile, 'utf8');
  
  // Check for App Router patterns
  if (content.includes('next/head')) {
    console.log('⚠️  Found next/head import - should use Metadata API for App Router');
    hasWarnings = true;
  }

  if (content.includes('Metadata')) {
    console.log('✅ Found Metadata API usage');
  } else {
    console.log('❌ Missing Metadata API usage');
    hasErrors = true;
  }
}

/**
 * Main validation function
 */
function main() {
  validateTranslationFiles();
  validateAppRouterStructure();
  validateServerUtilities();
  validateMiddleware();
  validateNextConfig();
  validateSEOComponents();

  console.log('\n📊 Validation Summary:');
  
  if (hasErrors) {
    console.log('❌ Validation failed with errors');
    process.exit(1);
  } else if (hasWarnings) {
    console.log('⚠️  Validation completed with warnings');
    process.exit(0);
  } else {
    console.log('✅ All validations passed!');
    console.log('\n🎉 Server-only i18n implementation is ready!');
    process.exit(0);
  }
}

// Run validation
main();
