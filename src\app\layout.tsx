import type { <PERSON>ada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import '../styles/globals.css';
import { ThemeProvider } from '@/providers/theme-provider';
import { AuthSessionProvider } from '@/providers/auth-session-provider';
import QueryClientProvider from '@/providers/query-client-provider';
import { Toaster } from '@/components/ui/toaster';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { createClient } from '@/lib/supabase/server';
import { getServerAuthState } from '@/features/auth/services/auth-state.service';
import { getServerLogger } from '@/lib/logger';
import { getLocale } from '@/lib/i18n/server';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
});

const logger = getServerLogger('RootLayout');

export const metadata: Metadata = {
  title: 'PassForge',
  description: 'Secure password management for everyone',
};

/**
 * Optimized root layout for the PassForge application.
 * Sets up global styles, fonts, and context providers.
 * Uses centralized auth state service to prevent authentication loading states.
 *
 * @param {object} props - The component's props.
 * @param {React.ReactNode} props.children - The child components to render.
 * @returns {JSX.Element} The root layout structure.
 */
export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>): Promise<JSX.Element> {
  let user = null;
  let locale = 'en'; // Default fallback

  try {
    // Get locale for html lang attribute
    locale = await getLocale();
  } catch (error) {
    // Fallback to 'en' if locale detection fails
    locale = 'en';
  }

  try {
    // Only attempt to get auth state if we're in a request context (not during static generation)
    const { user: authUser, error } = await getServerAuthState();
    user = authUser;

    if (error) {
      // Check if this is a static generation error
      if (error.message.includes('Dynamic server usage') || error.message.includes('cookies')) {
        // This is expected during static generation - don't log as error
        logger.info('Static generation detected, skipping auth state', {
          operation: 'RootLayout'
        });
      } else {
        // Actual auth error - log appropriately
        logger.warn('Error getting auth state in root layout', {
          error: error.message,
          stack: error.stack,
          operation: 'RootLayout'
        });
      }
    }
  } catch (err) {
    // Handle static generation errors gracefully
    if (err instanceof Error && (err.message.includes('Dynamic server usage') || err.message.includes('cookies'))) {
      // This is expected during static generation
      logger.info('Static generation detected, proceeding without auth state', {
        operation: 'RootLayout'
      });
    } else {
      // Unexpected error
      logger.error('Critical error in root layout', {
        error: err instanceof Error ? err.message : String(err),
        stack: err instanceof Error ? err.stack : undefined,
        operation: 'RootLayout'
      });
    }
  }

  return (
    <html lang={locale} suppressHydrationWarning>
      <body className={`${inter.variable} font-sans antialiased`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <AuthSessionProvider preloadedUser={user}>
            <QueryClientProvider>
              {children}
              <Toaster />
              {process.env.NODE_ENV === 'development' && <ReactQueryDevtools initialIsOpen={false} />}
            </QueryClientProvider>
          </AuthSessionProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
