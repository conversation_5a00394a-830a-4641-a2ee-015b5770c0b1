# Design Document

## Exemplos de Arquivos de Tradução

Todos os arquivos de tradução devem seguir o padrão kebab-case para nomes de arquivos e chaves. Exemplo:

**en/common.json**
```json
{
  "page-title": "Welcome",
  "submit-button": "Submit"
}
```

**pt/common.json**
```json
{
  "page-title": "Bem-vindo",
  "submit-button": "Enviar"
}
```

**es/common.json**
```json
{
  "page-title": "Bienvenido",
  "submit-button": "Enviar"
}
```

Para adicionar um novo idioma, crie uma nova pasta com o código do idioma (ex: `fr`) e traduza todos os arquivos existentes, mantendo a mesma estrutura e chaves.

## Overview

This design document outlines the architecture for a pure server-side translation system that eliminates client-side dependencies, optimizes SEO, and provides a seamless user experience. The solution follows the server-only approach recommended in the steering document, avoiding the router compatibility issues that caused previous implementation failures.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[User Request] --> B[Next.js Middleware]
    B --> C{Locale Detection}
    C --> D[Set x-locale Header]
    D --> E[Server Component]
    E --> F[Server-side Translation Loading]
    F --> G[Pre-rendered HTML with Translations]
    G --> H[Client Hydration]
    H --> I[Interactive Page]
    
    J[SEO Components] --> G
    K[Hreflang Generation] --> G
    L[Structured Data] --> G
    
    E --> M[Pass translations as props]
    M --> N[Client Components]
```

### Translation Loading Flow

```mermaid
sequenceDiagram
    participant Client
    participant Middleware
    participant Server
    participant FileSystem
    participant ServerComponent
    participant ClientComponent
    
    Client->>Middleware: Request /create-recipe
    Middleware->>Middleware: Detect locale (pt)
    Middleware->>Client: Redirect to /pt/create-recipe
    Middleware->>Middleware: Set x-locale: pt header
    Client->>Server: Request /pt/create-recipe
    Server->>FileSystem: Load pt translations
    FileSystem->>Server: Return namespace translations
    Server->>ServerComponent: Render with translations
    ServerComponent->>ClientComponent: Pass translations as props
    ServerComponent->>Client: Return pre-rendered HTML
```

## Components and Interfaces

### 1. Server Translation Utility (`src/lib/i18n/server.ts`)

The core of the server-only translation system:

```typescript
import { cache } from 'react';
import { headers } from 'next/headers';
import type { SupportedLocale } from '@/types/i18n';

// Get the current locale from headers (set by middleware)
export const getLocale = cache(async (): Promise<SupportedLocale> => {
  const headersList = headers();
  return (headersList.get('x-locale') || 'en') as SupportedLocale;
});

// Load translations for a specific namespace
export const getTranslations = cache(async (namespace: string) => {
  const locale = await getLocale();
  try {
    const translations = await import(`@/locales/${locale}/${namespace}.json`);
    return translations.default;
  } catch (error) {
    // Fallback to English if translation file is missing
    if (locale !== 'en') {
      try {
        const fallbackTranslations = await import(`@/locales/en/${namespace}.json`);
        // Log warning para traduções ausentes
        console.warn(`[i18n] Missing translation file for ${locale}/${namespace}, falling back to English`);
        return fallbackTranslations.default;
      } catch (fallbackError) {
        // Log erro crítico
        console.error(`[i18n] Missing translation file for en/${namespace}`);
        return {};
      }
    }
    console.error(`[i18n] Missing translation file for ${locale}/${namespace}`);
    return {};
  }
});

// Create a translator function for a specific namespace
export const createTranslator = cache(async (namespace: string) => {
  const translations = await getTranslations(namespace);
  
  return (key: string, params?: Record<string, any>): string => {
    const keyParts = key.includes(':') ? key.split(':') : [namespace, key];
    const ns = keyParts.length > 1 ? keyParts[0] : namespace;
    const k = keyParts.length > 1 ? keyParts[1] : keyParts[0];
    
    let translation = translations[k] || key;
    
    // Simple interpolation
    if (params) {
      Object.entries(params).forEach(([param, value]) => {
        translation = translation.replace(`{{${param}}}`, String(value));
      });
    }
    // Log warning para chaves ausentes em dev
    if (process.env.NODE_ENV === 'development' && !translations[k]) {
      console.warn(`[i18n] Missing translation key: ${k} in namespace: ${namespace}`);
    }
    return translation;
  };
});

// Get all translations for multiple namespaces
export const getAllTranslations = cache(async (namespaces: string[]) => {
  const locale = await getLocale();
  const translations: Record<string, Record<string, string>> = {};
  
  await Promise.all(
    namespaces.map(async (namespace) => {
      translations[namespace] = await getTranslations(namespace);
    })
  );
  
  return { locale, translations };
});

// Generate SEO metadata with translations
export const generateSEOMetadata = cache(async (namespace: string, pageKey: string) => {
  const locale = await getLocale();
  const translations = await getTranslations(namespace);
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://yoursite.com';
  
  const title = translations[`${pageKey}_meta_title`] || translations[`${pageKey}_title`] || pageKey;
  const description = translations[`${pageKey}_meta_description`] || '';
  
  // Generate hreflang links
  const path = headers().get('x-original-path') || '/';
  const hreflangLinks = ['en', 'pt', 'es'].map((lang) => ({
    hrefLang: lang,
    href: `${baseUrl}/${lang}${path}`
  }));
  
  return {
    title,
    description,
    locale,
    hreflangLinks,
    ogTitle: title,
    ogDescription: description,
    canonicalUrl: `${baseUrl}/${locale}${path}`
  };
});
```

### 2. SEO-Optimized Head Component (`src/components/seo/localized-head.tsx`)

```typescript
import { FC } from 'react';
import Head from 'next/head';

interface HreflangLink {
  hrefLang: string;
  href: string;
}

interface LocalizedHeadProps {
  title: string;
  description: string;
  keywords?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  hreflangLinks: HreflangLink[];
  structuredData?: any;
  canonicalUrl: string;
  locale: string;
}

export const LocalizedHead: FC<LocalizedHeadProps> = ({
  title,
  description,
  keywords,
  ogTitle,
  ogDescription,
  ogImage,
  hreflangLinks,
  structuredData,
  canonicalUrl,
  locale
}) => {
  return (
    <Head>
      <title>{title}</title>
      <meta name="description" content={description} />
      {keywords && <meta name="keywords" content={keywords} />}
      
      {/* Open Graph */}
      <meta property="og:title" content={ogTitle || title} />
      <meta property="og:description" content={ogDescription || description} />
      <meta property="og:locale" content={locale} />
      {ogImage && <meta property="og:image" content={ogImage} />}
      
      {/* Canonical URL */}
      <link rel="canonical" href={canonicalUrl} />
      
      {/* Hreflang links */}
      {hreflangLinks.map(({ hrefLang, href }) => (
        <link 
          key={hrefLang} 
          rel="alternate" 
          hrefLang={hrefLang} 
          href={href} 
        />
      ))}
      <link rel="alternate" hrefLang="x-default" href={hreflangLinks.find(l => l.hrefLang === 'en')?.href || canonicalUrl} />
      
      {/* Structured data */}
      {structuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
      )}
    </Head>
  );
};
```

### 3. Enhanced Middleware (`src/middleware.ts`)

```typescript
import { NextRequest, NextResponse } from 'next/server';
import type { SupportedLocale } from '@/types/i18n';

const SUPPORTED_LOCALES = ['en', 'pt', 'es'];
const DEFAULT_LOCALE = 'en';

export function middleware(request: NextRequest) {
  const { pathname, search } = request.nextUrl;
  
  // Skip for assets, api routes, etc.
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.includes('.')
  ) {
    return NextResponse.next();
  }
  
  // Check if URL already has a locale
  const pathnameHasLocale = SUPPORTED_LOCALES.some(
    locale => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  );
  
  if (pathnameHasLocale) {
    // Extract locale from URL
    const locale = pathname.split('/')[1] as SupportedLocale;
    
    // Create a response that passes through the request
    const response = NextResponse.next();
    
    // Set x-locale header for server components
    response.headers.set('x-locale', locale);
    response.headers.set('x-original-path', pathname.substring(locale.length + 1) || '/');
    
    return response;
  }
  
  // Detect locale preference
  let locale: SupportedLocale = DEFAULT_LOCALE;
  
  // 1. Check cookie first
  const cookieLocale = request.cookies.get('locale')?.value;
  if (cookieLocale && SUPPORTED_LOCALES.includes(cookieLocale as SupportedLocale)) {
    locale = cookieLocale as SupportedLocale;
  } 
  // 2. Then check Accept-Language header
  else {
    const acceptLanguage = request.headers.get('accept-language');
    if (acceptLanguage) {
      const preferredLocale = acceptLanguage
        .split(',')
        .map(lang => {
          const [langCode, weight] = lang.trim().split(';');
          return {
            code: langCode.substring(0, 2),
            weight: weight ? parseFloat(weight.split('=')[1]) : 1.0
          };
        })
        .sort((a, b) => b.weight - a.weight)
        .find(lang => SUPPORTED_LOCALES.includes(lang.code as SupportedLocale));
      
      if (preferredLocale) {
        locale = preferredLocale.code as SupportedLocale;
      }
    }
  }
  
  // Redirect to localized URL
  const url = request.nextUrl.clone();
  url.pathname = `/${locale}${pathname === '/' ? '' : pathname}`;
  url.search = search;
  
  // 302 redirect for SEO
  const response = NextResponse.redirect(url, 302);
  
  // Set locale cookie
  response.cookies.set('locale', locale, {
    path: '/',
    maxAge: 60 * 60 * 24 * 365, // 1 year
    sameSite: 'lax'
  });
  
  return response;
}

export const config = {
  matcher: ['/((?!_next|api|.*\\..*).*)']
};
```

### 4. Client Component Translation Props

For Client Components that need translations:

```typescript
// src/types/i18n.ts
export type SupportedLocale = 'en' | 'pt' | 'es';

export interface TranslationProps {
  translations: Record<string, string>;
  locale: SupportedLocale;
}

// Example Client Component
// src/components/client-only-button.tsx
'use client';

import { FC } from 'react';
import { TranslationProps } from '@/types/i18n';

interface ClientOnlyButtonProps extends TranslationProps {
  onClick: () => void;
}

export const ClientOnlyButton: FC<ClientOnlyButtonProps> = ({ 
  translations, 
  locale, 
  onClick 
}) => {
  return (
    <button onClick={onClick}>
      {translations.button_text}
    </button>
  );
};
```

### 5. Server Component with Client Component

```typescript
// src/app/[locale]/example/page.tsx
import { createTranslator, getLocale, getAllTranslations } from '@/lib/i18n/server';
import { ClientOnlyButton } from '@/components/client-only-button';

export default async function ExamplePage() {
  const t = await createTranslator('example');
  const locale = await getLocale();
  
  // Get translations for client component
  const { translations } = await getAllTranslations(['common']);
  
  return (
    <div>
      <h1>{t('example:title')}</h1>
      <p>{t('example:description')}</p>
      
      {/* Pass translations to client component */}
      <ClientOnlyButton 
        translations={{
          button_text: t('common:submit_button')
        }}
        locale={locale}
        onClick={() => {}} // Client-side handler
      />
    </div>
  );
}
```

## Data Models

### Translation Types

```typescript
// src/types/i18n.ts
export type SupportedLocale = 'en' | 'pt' | 'es';

export interface TranslationNamespace {
  [key: string]: string;
}

export interface TranslationsMap {
  [namespace: string]: TranslationNamespace;
}

export interface LocaleTranslations {
  locale: SupportedLocale;
  translations: TranslationsMap;
}

export interface SEOMetadata {
  title: string;
  description: string;
  keywords?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  canonicalUrl: string;
  locale: SupportedLocale;
  hreflangLinks: Array<{
    hrefLang: string;
    href: string;
  }>;
  structuredData?: any;
}
```

## Error Handling

### Translation Loading Errors

1. **Missing Translation File**
   - Fallback to English version
   - Log warning for missing translations
   - Continue with available translations

2. **Malformed Translation JSON**
   - Skip malformed namespace
   - Use fallback translations
   - Report error to monitoring system

3. **Missing Translation Key**
   - Return the key itself as fallback
   - Log warning in development mode
   - Continue rendering with available translations

### SEO Error Handling

1. **Missing SEO Metadata**
   - Use default values from configuration
   - Generate basic meta tags
   - Ensure hreflang links are always present

2. **Invalid Locale Detection**
   - Fallback to default locale (English)
   - Set appropriate cookies
   - Redirect to valid locale path

## Testing Strategy

### Integração com CI/CD

Os scripts de validação de traduções devem ser integrados ao pipeline de CI/CD. O build deve falhar caso haja chaves ausentes em qualquer idioma.

### Unit Testing

1. **Server Translation Utility Tests**
   ```typescript
   describe('Server Translation Utility', () => {
     test('getLocale returns correct locale from headers', async () => {
       // Mock headers
       jest.spyOn(require('next/headers'), 'headers').mockReturnValue({
         get: jest.fn().mockReturnValue('pt')
       });
       
       const locale = await getLocale();
       expect(locale).toBe('pt');
     });
     
     test('getTranslations loads correct namespace', async () => {
       // Mock import
       jest.mock('@/locales/en/common.json', () => ({
         hello: 'Hello'
       }), { virtual: true });
       
       const translations = await getTranslations('common');
       expect(translations).toHaveProperty('hello');
     });
     
     test('createTranslator returns translation function', async () => {
       // Setup
       jest.spyOn(require('next/headers'), 'headers').mockReturnValue({
         get: jest.fn().mockReturnValue('en')
       });
       
       // Mock translations
       jest.mock('@/locales/en/common.json', () => ({
         hello: 'Hello {{name}}'
       }), { virtual: true });
       
       const t = await createTranslator('common');
       expect(t('hello', { name: 'World' })).toBe('Hello World');
     });
   });
   ```

2. **SEO Component Tests**
   ```typescript
   describe('LocalizedHead Component', () => {
     test('renders correct hreflang links', () => {
       const props = {
         title: 'Test',
         description: 'Test description',
         locale: 'en',
         canonicalUrl: 'https://example.com/en/test',
         hreflangLinks: [
           { hrefLang: 'en', href: 'https://example.com/en/test' },
           { hrefLang: 'pt', href: 'https://example.com/pt/test' }
         ]
       };
       
       const { container } = render(<LocalizedHead {...props} />);
       expect(container.querySelector('link[hreflang="en"]')).toBeInTheDocument();
       expect(container.querySelector('link[hreflang="pt"]')).toBeInTheDocument();
     });
   });
   ```

### Integration Testing

1. **End-to-End SEO Testing**
   - Verify hreflang links in rendered HTML
   - Test locale detection and redirects
   - Validate structured data output

2. **Performance Testing**
   - Measure First Contentful Paint
   - Test Cumulative Layout Shift
   - Verify no FOUC occurs

### SEO Testing

1. **Crawler Simulation**
   ```bash
   # Test different Accept-Language headers
   curl -H "Accept-Language: pt-BR,pt;q=0.9" http://localhost:3000/create-recipe
   curl -H "Accept-Language: es-ES,es;q=0.9" http://localhost:3000/create-recipe
   ```

2. **Hreflang Validation**
   ```bash
   # Verify hreflang links are present
   curl -s http://localhost:3000/pt/create-recipe | grep -o 'hreflang="[^"]*"'
   ```

## Implementation Phases

### Phase 1: Core Server Translation Utility
- Implement `getLocale`, `getTranslations`, and `createTranslator` functions
- Add caching with React's cache function
- Implement fallback mechanism for missing translations

### Phase 2: Middleware Enhancement
- Update middleware to set x-locale header
- Implement locale detection logic
- Add cookie-based locale persistence

### Phase 3: SEO Components
- Create LocalizedHead component
- Implement generateSEOMetadata utility
- Add hreflang and structured data support

### Phase 4: Client Component Support
- Define TranslationProps interface
- Create pattern for passing translations to Client Components
- Document best practices for Client Components

### Phase 5: Page Migration
- Update App Router pages to use server-only translations
- Migrate critical pages first (homepage, create-recipe)
- Test and validate SEO improvements

### Phase 6: Validation and Tools
- Implement translation validation scripts
- Create sitemap generation utility
- Add performance monitoring

## Performance Considerations

### Server-Side Optimizations

1. **Translation Caching**
   - Use React's cache function for efficient reuse
   - Implement file system caching for build-time optimizations
   - Use CDN caching for static translation assets

2. **Bundle Optimization**
   - No client-side translation bundles
   - Reduced JavaScript payload
   - Improved Time to Interactive

### Client-Side Optimizations

1. **Minimal Client JavaScript**
   - No client-side translation loading
   - No hydration mismatches
   - Reduced bundle size

2. **Progressive Enhancement**
   - Core functionality works without JavaScript
   - Enhanced features added with minimal client code
   - Improved Core Web Vitals

## Security Considerations

### Input Validation
- Validate locale parameters to prevent injection attacks
- Sanitize translation content to prevent XSS
- Implement rate limiting for locale switching

### Content Security
- Ensure translation files are served with appropriate headers
- Implement integrity checks for translation assets
- Use secure cookies for locale preferences

## Monitoring and Analytics

### Performance Monitoring
- Track Core Web Vitals per locale
- Monitor server-side rendering times
- Measure SEO score improvements

### SEO Monitoring
- Track search engine indexing per locale
- Monitor hreflang implementation effectiveness
- Analyze organic traffic by language

### Error Monitoring
- Track translation loading failures
- Monitor locale detection accuracy
- Alert on SEO metadata generation errors

## Migration Strategy

### Migration from Client-Side to Server-Side

> **Documentação step-by-step:**
> Todas as etapas de migração devem ser documentadas seguindo o padrão "step-by-step" já adotado no projeto, facilitando a adoção incremental e revisão.

1. **Identify Pages to Migrate**
   - Start with high-traffic, SEO-critical pages
   - Prioritize pages with FOUC issues
   - Create migration schedule

2. **Update Page Structure**
   - Convert to App Router format if needed
   - Add server-side translation loading
   - Pass translations to Client Components as props

3. **Test and Validate**
   - Verify SEO improvements
   - Test performance metrics
   - Ensure no regression in functionality

This design ensures a pure server-side translation system that eliminates client-side dependencies, optimizes SEO, and provides a seamless user experience. By avoiding the router compatibility issues that caused previous implementation failures, this approach delivers a robust and maintainable solution.