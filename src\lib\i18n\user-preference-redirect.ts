/**
 * User preference-based redirect utilities for server-only i18n
 * Handles redirecting users to their preferred locale when visiting root paths
 */

import { redirect } from 'next/navigation';
import { getLocaleWithUserPreference } from './server';
import type { SupportedLocale } from '@/types/i18n';

/**
 * Redirect user to their preferred locale
 * Use this in pages that don't have a locale in the URL
 * 
 * @param requestedPath - The path the user is trying to access (without locale)
 * @param fallbackLocale - Fallback locale if user preference detection fails
 */
export async function redirectToUserPreferredLocale(
  requestedPath: string = '/',
  fallbackLocale: SupportedLocale = 'en'
): Promise<never> {
  try {
    // Get the user's preferred locale (includes user DB preference)
    const preferredLocale = await getLocaleWithUserPreference();
    
    // Construct the localized URL
    const localizedPath = `/${preferredLocale}${requestedPath === '/' ? '' : requestedPath}`;
    
    // Redirect to the localized version
    redirect(localizedPath);
  } catch (error) {
    // If anything fails, redirect to fallback locale
    const fallbackPath = `/${fallbackLocale}${requestedPath === '/' ? '' : requestedPath}`;
    redirect(fallbackPath);
  }
}

/**
 * Get the preferred locale for a user without redirecting
 * Useful for conditional logic or metadata generation
 */
export async function getUserPreferredLocale(): Promise<SupportedLocale> {
  try {
    return await getLocaleWithUserPreference();
  } catch (error) {
    return 'en'; // Safe fallback
  }
}

/**
 * Check if a user should be redirected based on their preferences
 * Returns the redirect URL if needed, null if no redirect required
 */
export async function getRedirectUrlForUserPreference(
  currentPath: string,
  currentLocale?: string
): Promise<string | null> {
  try {
    const preferredLocale = await getLocaleWithUserPreference(currentLocale);
    
    // If current locale matches preferred locale, no redirect needed
    if (currentLocale === preferredLocale) {
      return null;
    }
    
    // If no current locale in URL, redirect to preferred locale
    if (!currentLocale) {
      return `/${preferredLocale}${currentPath === '/' ? '' : currentPath}`;
    }
    
    // For now, we respect URL locale over user preference for SEO
    // But this could be customized based on business requirements
    return null;
  } catch (error) {
    return null; // No redirect on error
  }
}
