/**
 * Example page with SSR i18n and SEO optimization
 * Shows how to implement server-side translations for better SEO
 */

import { GetServerSideProps } from 'next';
import { useServerI18n } from '@/hooks/use-server-i18n';
import { LocalizedHead } from '@/components/seo/LocalizedHead';
import { getI18nServerSideProps } from '@/lib/i18n/server';
import type { NamespaceTranslations, SupportedLocale } from '@/lib/i18n';

interface CreateRecipePageProps {
  locale: SupportedLocale;
  translations: NamespaceTranslations;
  seoMetadata: {
    title: string;
    description: string;
    keywords: string;
    ogTitle: string;
    ogDescription: string;
  };
  hreflangLinks: Array<{
    hrefLang: string;
    href: string;
  }>;
}

export default function CreateRecipePage({
  locale,
  translations,
  seoMetadata,
  hreflangLinks,
}: CreateRecipePageProps) {
  const { t, switchLocale, getLocalizedPath } = useServerI18n({ locale, translations });

  // Structured data for SEO
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'WebApplication',
    name: t('create-recipe:title'),
    description: t('create-recipe:subtitle'),
    applicationCategory: 'HealthApplication',
    operatingSystem: 'Web',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
    },
  };

  return (
    <>
      <LocalizedHead
        title={seoMetadata.title}
        description={seoMetadata.description}
        keywords={seoMetadata.keywords}
        ogTitle={seoMetadata.ogTitle}
        ogDescription={seoMetadata.ogDescription}
        hreflangLinks={hreflangLinks}
        structuredData={structuredData}
      />
      
      <main>
        {/* Content is immediately available - no loading states! */}
        <h1>{t('create-recipe:title')}</h1>
        <p>{t('create-recipe:subtitle')}</p>
        
        {/* Step navigation */}
        <nav aria-label={t('create-recipe:navigation.stepsLabel')}>
          <h2>{t('create-recipe:steps.health-concern.title')}</h2>
          <p>{t('create-recipe:steps.health-concern.description')}</p>
        </nav>
        
        {/* Language switcher */}
        <div className="language-switcher">
          <button onClick={() => switchLocale('en')}>English</button>
          <button onClick={() => switchLocale('pt')}>Português</button>
          <button onClick={() => switchLocale('es')}>Español</button>
        </div>
        
        {/* Progress indicator */}
        <div className="progress">
          {t('create-recipe:navigation.progress', undefined, { current: 1, total: 6 })}
        </div>
      </main>
    </>
  );
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  return getI18nServerSideProps(context);
};